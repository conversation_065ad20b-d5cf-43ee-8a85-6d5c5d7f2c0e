const express = require('express');
const { chromium } = require('playwright');
const path = require('path');
const ProxyManager = require('./proxy-manager');

const app = express();
const PORT = process.env.PORT || 3003;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Route to serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route for progress updates (Server-Sent Events)
app.get('/progress/:sessionId', (req, res) => {
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
    });

    const sessionId = req.params.sessionId;

    // Store the response object for this session
    if (!global.progressSessions) {
        global.progressSessions = {};
    }
    global.progressSessions[sessionId] = res;

    // Clean up on client disconnect
    req.on('close', () => {
        delete global.progressSessions[sessionId];
    });
});

// Function to send progress update
function sendProgress(sessionId, progress, message, submessage = '') {
    if (global.progressSessions && global.progressSessions[sessionId]) {
        const data = JSON.stringify({ progress, message, submessage });
        global.progressSessions[sessionId].write(`data: ${data}\n\n`);
    }
}

// Route to extract product data
app.post('/extract-product', async (req, res) => {
    const { url, sessionId } = req.body;

    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }

    // Validate if it's an Alibaba URL
    if (!url.includes('alibaba.com')) {
        return res.status(400).json({ error: 'يجب أن يكون الرابط من موقع Alibaba' });
    }

    // إنشاء مدير البروكسيات
    const proxyManager = new ProxyManager();

    // يمكنك إضافة بروكسيات هنا
    // proxyManager.addProxy('proxy-host', 8080, 'username', 'password');

    let browser;
    let context;
    let page;

    try {
        sendProgress(sessionId, 10, 'جاري تشغيل المتصفح...', 'تحضير البيئة');
        console.log('Starting browser with Playwright...');

        // إعدادات المتصفح مع تحسينات stealth
        const browserOptions = {
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-accelerated-2d-canvas',
                '--no-first-run',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-blink-features=AutomationControlled',
                '--disable-extensions',
                '--disable-plugins',

                '--disable-default-apps',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-ipc-flooding-protection',
                '--no-default-browser-check',
                '--no-first-run',
                '--no-pings',
                '--password-store=basic',
                '--use-mock-keychain'
            ]
        };

        browser = await chromium.launch(browserOptions);

        // إعدادات السياق مع البروكسي وتحسينات stealth
        const contextOptions = {
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1920, height: 1080 },
            locale: 'en-US',
            timezoneId: 'America/New_York',
            permissions: ['geolocation'],
            geolocation: { latitude: 40.7128, longitude: -74.0060 }, // New York coordinates
            extraHTTPHeaders: {
                'Accept-Language': 'en-US,en;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
                'Upgrade-Insecure-Requests': '1',
                'Sec-Fetch-Site': 'none',
                'Sec-Fetch-Mode': 'navigate',
                'Sec-Fetch-User': '?1',
                'Cache-Control': 'max-age=0',
                'DNT': '1',
                'Connection': 'keep-alive'
            },
            javaScriptEnabled: true,
            ignoreHTTPSErrors: true
        };

        // إضافة البروكسي إذا كان متاحاً
        const proxy = proxyManager.getNextProxy();
        if (proxy) {
            const proxyConfig = proxyManager.formatProxyForPlaywright(proxy);
            contextOptions.proxy = proxyConfig;
            console.log(`Using proxy: ${proxy.host}:${proxy.port}`);
            sendProgress(sessionId, 15, 'جاري الاتصال عبر البروكسي...', `${proxy.host}:${proxy.port}`);
        }

        context = await browser.newContext(contextOptions);
        page = await context.newPage();

        // إعدادات stealth إضافية للصفحة
        await page.addInitScript(() => {
            // إخفاء webdriver property
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });

            // إخفاء automation properties
            delete navigator.__proto__.webdriver;

            // تعديل plugins
            Object.defineProperty(navigator, 'plugins', {
                get: () => [1, 2, 3, 4, 5],
            });

            // تعديل languages
            Object.defineProperty(navigator, 'languages', {
                get: () => ['en-US', 'en'],
            });

            // إخفاء chrome runtime
            if (navigator.chrome) {
                delete navigator.chrome.runtime;
            }

            // تعديل permissions
            const originalQuery = window.navigator.permissions.query;
            window.navigator.permissions.query = (parameters) => (
                parameters.name === 'notifications' ?
                    Promise.resolve({ state: Notification.permission }) :
                    originalQuery(parameters)
            );
        });

        sendProgress(sessionId, 25, 'جاري تحميل صفحة المنتج...', 'الاتصال بالخادم');
        console.log('Navigating to URL:', url);

        try {
            await page.goto(url, {
                waitUntil: 'networkidle',
                timeout: 45000
            });
        } catch (error) {
            console.log('Navigation error, trying with different proxy...');
            if (proxy) {
                proxyManager.markProxyAsFailed(proxy);
                // محاولة مع بروكسي آخر
                const newProxy = proxyManager.getNextProxy();
                if (newProxy && newProxy !== proxy) {
                    await context.close();
                    const newContextOptions = { ...contextOptions };
                    newContextOptions.proxy = proxyManager.formatProxyForPlaywright(newProxy);
                    context = await browser.newContext(newContextOptions);
                    page = await context.newPage();
                    console.log(`Retrying with new proxy: ${newProxy.host}:${newProxy.port}`);
                    await page.goto(url, {
                        waitUntil: 'networkidle',
                        timeout: 45000
                    });
                } else {
                    throw error;
                }
            } else {
                throw error;
            }
        }

        sendProgress(sessionId, 40, 'جاري انتظار تحميل المحتوى...', 'تحميل العناصر الأساسية');
        // انتظار أطول لتحميل المحتوى
        await page.waitForTimeout(10000);
        
        console.log('Extracting product data...');

        // Debug: Check what tables are available
        const availableTables = await page.evaluate(() => {
            const tables = document.querySelectorAll('table');
            return Array.from(tables).map((table, index) => {
                const rows = table.querySelectorAll('tr');
                const classes = table.className;
                const parent = table.parentElement ? table.parentElement.className : '';
                return {
                    index,
                    rowCount: rows.length,
                    classes,
                    parentClasses: parent,
                    hasKeyValue: rows.length > 0 ? Array.from(rows).slice(0, 3).some(row => {
                        const cells = row.querySelectorAll('td');
                        return cells.length >= 2 && cells[0].textContent.trim() && cells[1].textContent.trim();
                    }) : false
                };
            });
        });

        console.log('Available tables:', availableTables);
        
        sendProgress(sessionId, 55, 'جاري النقر على التبويبات...', 'تحميل المحتوى الديناميكي');

        // البحث عن التبويبات والنقر عليها
        const tabs = await page.locator('[role="tab"], button[data-testid*="tab"]').all();
        console.log(`Found ${tabs.length} tabs to click`);

        // النقر على كل تبويب مع انتظار
        for (let i = 0; i < tabs.length; i++) {
            try {
                const tabText = await tabs[i].textContent();
                console.log(`Clicking tab ${i + 1}: ${tabText?.trim()}`);

                await tabs[i].click();
                await page.waitForTimeout(2000); // انتظار 2 ثانية بعد كل نقرة

                console.log(`Tab ${i + 1} clicked, waiting for content...`);
            } catch (error) {
                console.log(`Error clicking tab ${i + 1}:`, error.message);
            }
        }

        console.log(`Clicked ${tabs.length} tabs, waiting for content to load...`);

        // انتظار إضافي لتحميل المحتوى
        await page.waitForTimeout(5000);

        sendProgress(sessionId, 70, 'جاري انتظار تحميل البيانات...', 'تحميل الجداول والمواصفات');

        // انتظار عناصر محددة للتحميل
        try {
            await page.waitForSelector('.attribute-info, table', { timeout: 20000 });
            console.log('Found attribute elements');
        } catch (e) {
            console.log('No specific elements found, waiting longer...');
        }

        // انتظار أطول لتحميل جميع المحتويات بعد النقر على التبويبات
        await page.waitForTimeout(15000);

        sendProgress(sessionId, 85, 'جاري استخراج البيانات...', 'تحليل العناصر والجداول');
        console.log('Extracting product data...');

        // Extract product data
        const productData = await page.evaluate(() => {
            const data = {
                title: '',
                description: '',
                images: [],
                features: []
            };

            // Extract title - Updated selectors for new Alibaba layout
            const titleSelectors = [
                'h1[data-role="titleBox"]',
                '.product-title h1',
                '.ma-title h1',
                'h1.ma-title',
                '.product-name h1',
                'h1[data-testid*="title"]',
                'h1',
                '.title h1'
            ];

            for (const selector of titleSelectors) {
                const titleElement = document.querySelector(selector);
                if (titleElement && titleElement.textContent.trim()) {
                    data.title = titleElement.textContent.trim();
                    break;
                }
            }
            
            // Extract description - Updated for new Alibaba layout with detailed tables
            let description = '';

            // Try to click on description tab first
            const descriptionTab = document.querySelector('[role="tab"][id*="description"], button[data-testid*="description"]');
            if (descriptionTab) {
                descriptionTab.click();
                // Wait a bit for content to load
                setTimeout(() => {}, 2000);
            }

            // Also try clicking on any tab that might contain description
            const allTabs = document.querySelectorAll('[role="tab"]');
            allTabs.forEach(tab => {
                const tabText = tab.textContent.toLowerCase();
                if (tabText.includes('وصف') || tabText.includes('description') || tabText.includes('detail')) {
                    tab.click();
                    setTimeout(() => {}, 1000);
                }
            });

            // Extract from detailed table - comprehensive search
            let detailTable = null;
            let allCandidateTables = [];

            // Get ALL tables and analyze them
            const allTables = document.querySelectorAll('table');

            allTables.forEach((table) => {
                const rows = table.querySelectorAll('tr');
                if (rows.length >= 2) {
                    let keyValuePairs = 0;
                    let totalContent = 0;

                    // Analyze first few rows to see if it's a description table
                    for (let i = 0; i < Math.min(10, rows.length); i++) {
                        const cells = rows[i].querySelectorAll('td');
                        if (cells.length >= 2) {
                            const key = cells[0].textContent.trim();
                            const value = cells[1].textContent.trim();
                            if (key.length > 0 && value.length > 0) {
                                keyValuePairs++;
                                totalContent += key.length + value.length;
                            }
                        }
                    }

                    // Score the table based on content and structure
                    const score = keyValuePairs * 10 + totalContent;

                    if (keyValuePairs >= 2) {
                        allCandidateTables.push({
                            table,
                            score,
                            keyValuePairs,
                            rowCount: rows.length,
                            classes: table.className,
                            parentClasses: table.parentElement ? table.parentElement.className : ''
                        });
                    }
                }
            });

            // Sort by score and pick the best table
            allCandidateTables.sort((a, b) => b.score - a.score);

            // Prefer tables with specific classes if they exist
            const preferredTable = allCandidateTables.find(t =>
                t.classes.includes('magic-3') ||
                t.classes.includes('hight-light-first-column') ||
                t.parentClasses.includes('ife-detail-decorate-table')
            );

            detailTable = preferredTable ? preferredTable.table :
                         (allCandidateTables.length > 0 ? allCandidateTables[0].table : null);

            if (detailTable) {
                const rows = detailTable.querySelectorAll('tr');
                let tableData = [];

                console.log(`Found description table with ${rows.length} rows`);

                rows.forEach((row, index) => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 2) {
                        const key = cells[0].textContent.trim().replace(/:/g, '');
                        const value = cells[1].textContent.trim();

                        if (key && value && key.length > 0 && value.length > 0) {
                            // Clean up the value text but keep more content
                            const cleanValue = value.replace(/\s+/g, ' ').trim();
                            if (cleanValue.length > 0) {
                                tableData.push(`**${key}**: ${cleanValue}`);
                                console.log(`Row ${index + 1}: ${key} = ${cleanValue.substring(0, 50)}...`);
                            }
                        }
                    }
                });

                if (tableData.length > 0) {
                    description = tableData.join('\n\n');
                    console.log(`Extracted ${tableData.length} description items`);
                } else {
                    console.log('No valid description data found in table');
                }
            } else {
                console.log('No description table found');
            }

            // Fallback to traditional selectors if no table found
            if (!description) {
                const descriptionSelectors = [
                    '[data-state="active"][id*="description"]',
                    '[role="tabpanel"][id*="description"]',
                    '.ma-ref-content .ma-ref-description',
                    '.product-description',
                    '.ma-description',
                    '.description-content',
                    '.product-detail-description',
                    '[data-role="description"]',
                    '.product-overview',
                    '.product-intro',
                    '.detail-desc',
                    '.product-summary'
                ];

                for (const selector of descriptionSelectors) {
                    const descElement = document.querySelector(selector);
                    if (descElement && descElement.textContent.trim()) {
                        let desc = descElement.textContent.trim();
                        // Clean up description
                        desc = desc.replace(/\s+/g, ' ').substring(0, 800);
                        if (desc.length > 50) {
                            description = desc;
                            break;
                        }
                    }
                }
            }

            data.description = description;
            
            // Extract images - Updated for new Alibaba layout
            const imageUrls = new Set();

            // First try to get images from background-image style (new layout)
            const backgroundImageElements = document.querySelectorAll('[style*="background-image"]');
            backgroundImageElements.forEach(element => {
                const style = element.getAttribute('style');
                if (style && style.includes('background-image')) {
                    const match = style.match(/background-image:\s*url\(["']?(.*?)["']?\)/);
                    if (match && match[1]) {
                        let src = match[1];
                        if (src.startsWith('//')) {
                            src = 'https:' + src;
                        }
                        // Clean up image URL for better quality
                        if (src.includes('_80x80.jpg')) {
                            src = src.replace('_80x80.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_50x50.jpg')) {
                            src = src.replace('_50x50.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_100x100.jpg')) {
                            src = src.replace('_100x100.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_220x220.jpg')) {
                            src = src.replace('_220x220.jpg', '_400x400.jpg');
                        }
                        if (src.includes('alicdn.com') && src.includes('.jpg')) {
                            imageUrls.add(src);
                        }
                    }
                }
            });

            // Fallback to traditional img tags if no background images found
            if (imageUrls.size === 0) {
                const imageSelectors = [
                    '.ma-thumb-list img',
                    '.product-images img',
                    '.ma-preview-list img',
                    '.image-gallery img',
                    '.product-gallery img',
                    '.gallery-img img',
                    '.main-image img',
                    'img[src*="alicdn.com"]'
                ];

                for (const selector of imageSelectors) {
                    const images = document.querySelectorAll(selector);
                    images.forEach(img => {
                        let src = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src') || img.getAttribute('data-original');
                        if (src && !src.includes('data:image') && (src.startsWith('http') || src.startsWith('//'))) {
                            // Fix protocol if missing
                            if (src.startsWith('//')) {
                                src = 'https:' + src;
                            }
                            // Clean up image URL for better quality
                            if (src.includes('_80x80.jpg')) {
                                src = src.replace('_80x80.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_50x50.jpg')) {
                                src = src.replace('_50x50.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_100x100.jpg')) {
                                src = src.replace('_100x100.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_220x220.jpg')) {
                                src = src.replace('_220x220.jpg', '_400x400.jpg');
                            }
                            imageUrls.add(src);
                        }
                    });
                    if (imageUrls.size > 0) break;
                }
            }
            
            data.images = Array.from(imageUrls).slice(0, 8); // Limit to 8 images
            
            // Extract features/specifications - Updated for new Alibaba layout
            // Try to click on attributes tab first
            const attributesTab = document.querySelector('[role="tab"][id*="attribute"], button[data-testid*="attribute"]');
            if (attributesTab) {
                attributesTab.click();
                // Wait a bit for content to load
                setTimeout(() => {}, 1000);
            }

            // Extract from attribute-info section (main specifications)
            const attributeInfo = document.querySelector('.attribute-info');
            if (attributeInfo) {
                console.log('Found attribute-info section');
                const attributeSections = attributeInfo.querySelectorAll('h3');
                attributeSections.forEach(section => {
                    const sectionTitle = section.textContent.trim();
                    if (sectionTitle) {
                        data.features.push(`=== ${sectionTitle} ===`);
                        console.log(`Found section: ${sectionTitle}`);

                        // Get the attribute list following this section
                        let nextElement = section.nextElementSibling;
                        if (nextElement && nextElement.classList.contains('attribute-list')) {
                            const items = nextElement.querySelectorAll('.attribute-item');
                            console.log(`Found ${items.length} items in section ${sectionTitle}`);
                            items.forEach(item => {
                                const left = item.querySelector('.left');
                                const right = item.querySelector('.right');
                                if (left && right) {
                                    const key = left.textContent.trim();
                                    const value = right.textContent.trim();
                                    if (key && value) {
                                        data.features.push(`${key}: ${value}`);
                                    }
                                }
                            });
                        }
                    }
                });
            } else {
                console.log('No attribute-info section found');
            }

            // If no attribute-info found, try to extract from other specification sources
            // (Skip description tables as they are now handled in description section)

            // Extract from SKU options (colors, variants) - Additional specifications
            const skuLayout = document.querySelector('[data-testid="sku-layout"]');
            if (skuLayout) {
                data.features.push('=== مواصفات إضافية ===');

                const skuLists = skuLayout.querySelectorAll('[data-testid="sku-list"]');
                skuLists.forEach(list => {
                    const title = list.querySelector('[data-testid="sku-list-title"]');
                    if (title) {
                        const titleText = title.textContent.trim();
                        if (titleText) {
                            data.features.push(titleText);
                        }
                    }
                });

                // Extract shipping info
                const logistic = skuLayout.querySelector('[data-testid="sku-logistic"]');
                if (logistic) {
                    const logisticText = logistic.textContent.trim();
                    if (logisticText && logisticText.length > 10) {
                        data.features.push(`الشحن: ${logisticText.substring(0, 200)}`);
                    }
                }
            }

            // Extract from price and product highlights
            const priceInfo = document.querySelector('[data-testid="product-price"]');
            if (priceInfo) {
                const priceText = priceInfo.textContent.trim();
                if (priceText.includes('US$')) {
                    const priceMatch = priceText.match(/[\d.,]+-[\d.,]+\s*US\$/);
                    if (priceMatch) {
                        data.features.push(`السعر: ${priceMatch[0]}`);
                    }
                }
                if (priceText.includes('الحد الأدنى للطلب')) {
                    const minOrderMatch = priceText.match(/الحد الأدنى للطلب\s+(\d+)\s*قطعة/);
                    if (minOrderMatch) {
                        data.features.push(`الحد الأدنى للطلب: ${minOrderMatch[1]} قطعة`);
                    }
                }
            }

            // Extract from product highlights
            const highlights = document.querySelectorAll('[data-module-name="module_product_highlights"] [data-testid]');
            highlights.forEach(highlight => {
                const text = highlight.textContent.trim();
                if (text && text.length > 3 && text.length < 100 && !text.includes('قابل للتخصيص')) {
                    data.features.push(text);
                }
            });

            // Traditional selectors as fallback
            if (data.features.length === 0) {
                const featureSelectors = [
                    '[data-state="active"][id*="attribute"] li',
                    '[role="tabpanel"][id*="attribute"] li',
                    '.ma-spec-list li',
                    '.product-specs li',
                    '.specifications li',
                    '.product-features li',
                    '.ma-attribute-list li'
                ];

                for (const selector of featureSelectors) {
                    const features = document.querySelectorAll(selector);
                    if (features.length > 0) {
                        features.forEach(feature => {
                            const text = feature.textContent.trim();
                            if (text && text.length > 3 && text.length < 200) {
                                data.features.push(text);
                            }
                        });
                        break;
                    }
                }

                // If still no features found, try to extract from table rows
                if (data.features.length === 0) {
                    const tableRows = document.querySelectorAll('.ma-spec-table tr, .product-table tr, table tr');
                    tableRows.forEach(row => {
                        const cells = row.querySelectorAll('td, th');
                        if (cells.length >= 2) {
                            const key = cells[0].textContent.trim();
                            const value = cells[1].textContent.trim();
                            if (key && value && key.length < 50 && value.length < 100) {
                                data.features.push(`${key}: ${value}`);
                            }
                        }
                    });
                }
            }
            
            return data;
        });
        
        console.log('Product data extracted:', {
            title: productData.title ? 'Found' : 'Not found',
            description: productData.description ? 'Found' : 'Not found',
            images: productData.images.length,
            features: productData.features.length
        });

        // Check if we got enough data, if not retry once
        if (productData.features.length < 10) {
            console.log(`Insufficient data detected (${productData.features.length} features), retrying extraction...`);
            sendProgress(sessionId, 90, 'البيانات غير مكتملة، جاري إعادة المحاولة...', 'انتظار إضافي لتحميل البيانات');

            // النقر على المزيد من التبويبات والانتظار أطول
            const retryTabs = await page.locator('[role="tab"]').all();
            for (const tab of retryTabs) {
                try {
                    const text = await tab.textContent();
                    const textLower = text?.toLowerCase() || '';
                    if (textLower.includes('attribute') || textLower.includes('spec') || textLower.includes('detail') ||
                        textLower.includes('مواصفات') || textLower.includes('السمة') || textLower.includes('كفاءة')) {
                        console.log('Clicking on:', text);
                        await tab.click();
                        await page.waitForTimeout(1000);
                    }
                } catch (error) {
                    console.log('Error clicking retry tab:', error.message);
                }
            }

            // انتظار أطول لتحميل المحتوى
            await page.waitForTimeout(15000);

            // Re-extract data with the same logic
            const retryData = await page.evaluate(() => {
                const data = {
                    title: '',
                    description: '',
                    images: [],
                    features: []
                };

                // Check for attribute-info again
                const attributeInfo = document.querySelector('.attribute-info');
                if (attributeInfo) {
                    console.log('Found attribute-info on retry');
                    const attributeSections = attributeInfo.querySelectorAll('h3');
                    attributeSections.forEach(section => {
                        const sectionTitle = section.textContent.trim();
                        if (sectionTitle) {
                            data.features.push(`=== ${sectionTitle} ===`);

                            let nextElement = section.nextElementSibling;
                            if (nextElement && nextElement.classList.contains('attribute-list')) {
                                const items = nextElement.querySelectorAll('.attribute-item');
                                items.forEach(item => {
                                    const left = item.querySelector('.left');
                                    const right = item.querySelector('.right');
                                    if (left && right) {
                                        const key = left.textContent.trim();
                                        const value = right.textContent.trim();
                                        if (key && value) {
                                            data.features.push(`${key}: ${value}`);
                                        }
                                    }
                                });
                            }
                        }
                    });
                } else {
                    console.log('Still no attribute-info found on retry');
                }

                return data;
            });

            // Use retry data if it's better
            if (retryData.features.length > productData.features.length) {
                console.log(`Retry successful! Got ${retryData.features.length} features instead of ${productData.features.length}`);
                productData.features = [...productData.features, ...retryData.features];
            } else {
                console.log('Retry did not improve results');
            }
        }

        sendProgress(sessionId, 95, 'جاري معالجة النتائج...', 'تنظيف وتنسيق البيانات');

        // Validate extracted data
        if (!productData.title && !productData.description && productData.images.length === 0 && productData.features.length === 0) {
            throw new Error('لم يتم العثور على بيانات المنتج. تأكد من صحة الرابط.');
        }

        // If no title found, try to extract from URL or use a default
        if (!productData.title) {
            const urlParts = url.split('/');
            const lastPart = urlParts[urlParts.length - 1];
            if (lastPart && lastPart.includes('_')) {
                const titleFromUrl = lastPart.split('_')[0].replace(/-/g, ' ');
                productData.title = titleFromUrl || 'منتج من Alibaba';
            } else {
                productData.title = 'منتج من Alibaba';
            }
        }

        sendProgress(sessionId, 100, 'تم الانتهاء!', 'جاري عرض النتائج');

        // Clean up the session
        setTimeout(() => {
            if (global.progressSessions && global.progressSessions[sessionId]) {
                global.progressSessions[sessionId].end();
                delete global.progressSessions[sessionId];
            }
        }, 2000);

        res.json(productData);
        
    } catch (error) {
        console.error('Error extracting product data:', error);
        res.status(500).json({ 
            error: 'حدث خطأ أثناء استخراج البيانات: ' + error.message 
        });
    } finally {
        if (context) {
            await context.close();
        }
        if (browser) {
            await browser.close();
        }
    }
});

// Start server
app.listen(PORT, () => {
    console.log(`🚀 Server is running on http://localhost:${PORT}`);
    console.log('📝 Open your browser and navigate to the URL above to use the Alibaba product extractor');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});
