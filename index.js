const express = require('express');
const { chromium } = require('playwright');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Route to serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route for progress updates (Server-Sent Events)
app.get('/progress/:sessionId', (req, res) => {
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
    });

    const sessionId = req.params.sessionId;

    // Store the response object for this session
    if (!global.progressSessions) {
        global.progressSessions = {};
    }
    global.progressSessions[sessionId] = res;

    // Clean up on client disconnect
    req.on('close', () => {
        delete global.progressSessions[sessionId];
    });
});

// Function to send progress update
function sendProgress(sessionId, progress, message, submessage = '') {
    if (global.progressSessions && global.progressSessions[sessionId]) {
        const data = JSON.stringify({ progress, message, submessage });
        global.progressSessions[sessionId].write(`data: ${data}\n\n`);
    }
}

// Route to extract product data - FAST VERSION
app.post('/extract-product', async (req, res) => {
    const { url, sessionId } = req.body;

    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }

    // Validate if it's an Alibaba URL
    if (!url.includes('alibaba.com')) {
        return res.status(400).json({ error: 'يجب أن يكون الرابط من موقع Alibaba' });
    }

    let browser;
    try {
        sendProgress(sessionId, 10, 'جاري تشغيل المتصفح...', 'تحضير البيئة');
        console.log('Starting fast browser...');
        
        // إعدادات متصفح محسنة للسرعة القصوى
        browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--no-first-run',
                '--no-default-browser-check',
                '--disable-extensions',
                '--disable-plugins',
                '--disable-images', // تعطيل الصور للسرعة
                '--disable-javascript', // تعطيل JS غير الضروري
                '--single-process'
            ]
        });

        const page = await browser.newPage({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });

        sendProgress(sessionId, 30, 'جاري تحميل الصفحة...', 'تحميل سريع');
        console.log('Loading page:', url);
        
        // تحميل سريع جداً
        await page.goto(url, {
            waitUntil: 'domcontentloaded',
            timeout: 15000
        });

        sendProgress(sessionId, 60, 'جاري استخراج البيانات...', 'استخراج سريع');
        
        // استخراج البيانات من الأقسام المحددة
        const productData = await page.evaluate(() => {
            const data = {
                title: '',
                description: '',
                images: [],
                features: [],
                sku_info: {},
                shipping_info: ''
            };

            // 1. استخراج العنوان
            const titleSelectors = ['h1[data-role="titleBox"]', '.product-title h1', 'h1'];
            for (const selector of titleSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    data.title = element.textContent.trim();
                    break;
                }
            }

            // 2. استخراج الصور من background-image
            const imageElements = document.querySelectorAll('[style*="background-image"]');
            const imageUrls = new Set();

            imageElements.forEach(element => {
                const style = element.getAttribute('style');
                if (style && style.includes('background-image')) {
                    const match = style.match(/background-image:\s*url\(["']?(.*?)["']?\)/);
                    if (match && match[1]) {
                        let src = match[1];
                        if (src.startsWith('//')) src = 'https:' + src;
                        // تحسين جودة الصورة
                        src = src.replace('_80x80.jpg', '_400x400.jpg');
                        src = src.replace('_50x50.jpg', '_400x400.jpg');
                        src = src.replace('_100x100.jpg', '_400x400.jpg');
                        if (src.includes('alicdn.com') && src.includes('.jpg')) {
                            imageUrls.add(src);
                        }
                    }
                }
            });
            data.images = Array.from(imageUrls).slice(0, 8);

            // 3. استخراج معلومات SKU (الألوان والأحجام)
            const skuModule = document.querySelector('[data-module-name="module_sku"]');
            if (skuModule) {
                // استخراج الألوان والأحجام
                const skuLists = skuModule.querySelectorAll('[data-testid="sku-list"]');
                skuLists.forEach(list => {
                    const title = list.querySelector('[data-testid="sku-list-title"]');
                    if (title) {
                        const titleText = title.textContent.trim();
                        data.features.push(`=== ${titleText} ===`);
                    }
                });

                // استخراج معلومات الشحن
                const logistic = skuModule.querySelector('[data-testid="sku-logistic"]');
                if (logistic) {
                    const logisticText = logistic.textContent.trim();
                    if (logisticText) {
                        data.shipping_info = logisticText;
                        data.features.push(`الشحن: ${logisticText}`);
                    }
                }
            }

            // 4. استخراج المواصفات من module_attribute
            const attributeModule = document.querySelector('[data-module-name="module_attribute"]');
            if (attributeModule) {
                const attributeInfo = attributeModule.querySelector('.attribute-info');
                if (attributeInfo) {
                    // استخراج الأقسام والمواصفات
                    const sections = attributeInfo.querySelectorAll('h3');
                    sections.forEach(section => {
                        const sectionTitle = section.textContent.trim();
                        if (sectionTitle) {
                            data.features.push(`=== ${sectionTitle} ===`);

                            // البحث عن قائمة المواصفات التالية
                            let nextElement = section.nextElementSibling;
                            if (nextElement && nextElement.classList.contains('attribute-list')) {
                                const items = nextElement.querySelectorAll('.attribute-item');
                                items.forEach(item => {
                                    const left = item.querySelector('.left');
                                    const right = item.querySelector('.right');
                                    if (left && right) {
                                        const key = left.textContent.trim();
                                        const value = right.textContent.trim();
                                        if (key && value) {
                                            data.features.push(`${key}: ${value}`);
                                            // إضافة للوصف أيضاً
                                            data.description += `**${key}**: ${value}\n\n`;
                                        }
                                    }
                                });
                            }
                        }
                    });
                }
            }

            return data;
        });

        sendProgress(sessionId, 90, 'جاري معالجة النتائج...', 'تنظيف البيانات');

        // تنظيف البيانات
        if (!productData.title) {
            productData.title = 'منتج من Alibaba';
        }

        sendProgress(sessionId, 100, 'تم الانتهاء!', 'جاري عرض النتائج');

        // Clean up the session
        setTimeout(() => {
            if (global.progressSessions && global.progressSessions[sessionId]) {
                global.progressSessions[sessionId].end();
                delete global.progressSessions[sessionId];
            }
        }, 2000);

        console.log('Extraction completed:', {
            title: productData.title ? 'Found' : 'Not found',
            description: productData.description ? 'Found' : 'Not found',
            images: productData.images.length,
            features: productData.features.length
        });

        res.json(productData);
        
    } catch (error) {
        console.error('Error extracting product data:', error);
        res.status(500).json({ 
            error: 'حدث خطأ أثناء استخراج البيانات: ' + error.message 
        });
    } finally {
        if (browser) {
            await browser.close();
        }
    }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 FAST Server is running on http://0.0.0.0:${PORT}`);
    console.log(`📝 Access the application at: http://localhost:${PORT}`);
    console.log(`🌐 External access: http://**************:${PORT}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});
