// اختبار مع رابط حقيقي من Alibaba
const { default: fetch } = require('node-fetch');

async function testRealAlibaba() {
    console.log('🧪 اختبار مع رابط حقيقي من Alibaba...');
    const startTime = Date.now();
    
    try {
        // رابط حقيقي من Alibaba (يجب أن يحتوي على البيانات المطلوبة)
        const testUrl = 'https://arabic.alibaba.com/product-detail/Chinese-Factory-2-Caps-1-Tamper-1600938692268.html';
        const sessionId = 'test_real_' + Date.now();
        
        console.log('📡 إرسال طلب الاستخراج...');
        console.log('🔗 الرابط:', testUrl);
        
        const response = await fetch('http://localhost:3004/extract-product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: testUrl,
                sessionId: sessionId
            })
        });
        
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`HTTP ${response.status}: ${errorText}`);
        }
        
        const data = await response.json();
        const totalTime = Date.now() - startTime;
        
        console.log('\n✅ نتائج الاختبار:');
        console.log(`⏱️  إجمالي الوقت: ${totalTime}ms (${(totalTime/1000).toFixed(1)} ثانية)`);
        console.log(`📝 العنوان: ${data.title ? '✅ موجود' : '❌ غير موجود'}`);
        console.log(`📄 الوصف: ${data.description ? '✅ موجود' : '❌ غير موجود'} (${data.description?.length || 0} حرف)`);
        console.log(`🖼️  الصور: ${data.images?.length || 0} صورة`);
        console.log(`⚙️  المواصفات: ${data.features?.length || 0} مواصفة`);
        console.log(`🚚 معلومات الشحن: ${data.shipping_info ? '✅ موجود' : '❌ غير موجود'}`);
        
        if (totalTime < 10000) {
            console.log('\n🚀 الأداء ممتاز! (أقل من 10 ثواني)');
        } else if (totalTime < 20000) {
            console.log('\n✅ الأداء جيد (أقل من 20 ثانية)');
        } else {
            console.log('\n⚠️  الأداء بطيء (أكثر من 20 ثانية)');
        }
        
        // عرض عينة من البيانات المستخرجة
        if (data.title) {
            console.log(`\n📋 عينة من البيانات المستخرجة:`);
            console.log(`العنوان: ${data.title.substring(0, 80)}...`);
            
            if (data.description) {
                console.log(`\nالوصف (أول 200 حرف):`);
                console.log(data.description.substring(0, 200) + '...');
            }
            
            if (data.images.length > 0) {
                console.log(`\nالصور المستخرجة:`);
                data.images.slice(0, 3).forEach((img, i) => {
                    console.log(`${i + 1}. ${img}`);
                });
                if (data.images.length > 3) {
                    console.log(`... و ${data.images.length - 3} صورة أخرى`);
                }
            }
            
            if (data.features.length > 0) {
                console.log(`\nالمواصفات المستخرجة (أول 5):`);
                data.features.slice(0, 5).forEach((feature, i) => {
                    console.log(`${i + 1}. ${feature}`);
                });
                if (data.features.length > 5) {
                    console.log(`... و ${data.features.length - 5} مواصفة أخرى`);
                }
            }
            
            if (data.shipping_info) {
                console.log(`\nمعلومات الشحن:`);
                console.log(data.shipping_info);
            }
        }
        
        // تحليل جودة البيانات
        console.log('\n📊 تحليل جودة البيانات:');
        let score = 0;
        if (data.title && data.title !== 'منتج من Alibaba') score += 25;
        if (data.description && data.description.length > 50) score += 25;
        if (data.images && data.images.length > 0) score += 25;
        if (data.features && data.features.length > 5) score += 25;
        
        console.log(`نقاط الجودة: ${score}/100`);
        if (score >= 75) {
            console.log('🌟 جودة ممتازة!');
        } else if (score >= 50) {
            console.log('👍 جودة جيدة');
        } else {
            console.log('⚠️  جودة منخفضة - قد تحتاج لتحسين');
        }
        
    } catch (error) {
        const totalTime = Date.now() - startTime;
        console.error('\n❌ خطأ في الاختبار:', error.message);
        console.log(`⏱️  الوقت حتى الخطأ: ${totalTime}ms`);
        
        // تحليل نوع الخطأ
        if (error.message.includes('Timeout')) {
            console.log('💡 نصيحة: قد تحتاج لزيادة timeout أو تحسين الشبكة');
        } else if (error.message.includes('404') || error.message.includes('403')) {
            console.log('💡 نصيحة: تحقق من صحة الرابط أو استخدم رابط آخر');
        } else if (error.message.includes('ECONNREFUSED')) {
            console.log('💡 نصيحة: تأكد من تشغيل الخادم على المنفذ 3004');
        }
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testRealAlibaba();
}
