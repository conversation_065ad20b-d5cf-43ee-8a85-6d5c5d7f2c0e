// اختبار سريع للتطبيق الجديد
const { default: fetch } = require('node-fetch');

async function testFastExtraction() {
    console.log('🧪 اختبار التطبيق السريع...');
    const startTime = Date.now();
    
    try {
        // رابط اختبار من Alibaba
        const testUrl = 'https://www.alibaba.com/product-detail/Custom-Logo-Wireless-Bluetooth-Earphones_1600000000000.html';
        const sessionId = 'test_' + Date.now();
        
        console.log('📡 إرسال طلب الاستخراج...');
        
        const response = await fetch('http://localhost:3004/extract-product', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                url: testUrl,
                sessionId: sessionId
            })
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        const totalTime = Date.now() - startTime;
        
        console.log('\n✅ نتائج الاختبار:');
        console.log(`⏱️  إجمالي الوقت: ${totalTime}ms (${(totalTime/1000).toFixed(1)} ثانية)`);
        console.log(`📝 العنوان: ${data.title ? '✅ موجود' : '❌ غير موجود'}`);
        console.log(`📄 الوصف: ${data.description ? '✅ موجود' : '❌ غير موجود'} (${data.description?.length || 0} حرف)`);
        console.log(`🖼️  الصور: ${data.images?.length || 0} صورة`);
        console.log(`⚙️  المواصفات: ${data.features?.length || 0} مواصفة`);
        
        if (totalTime < 10000) {
            console.log('\n🚀 الأداء ممتاز! (أقل من 10 ثواني)');
        } else if (totalTime < 20000) {
            console.log('\n✅ الأداء جيد (أقل من 20 ثانية)');
        } else {
            console.log('\n⚠️  الأداء بطيء (أكثر من 20 ثانية)');
        }
        
        // عرض عينة من البيانات
        if (data.title) {
            console.log(`\n📋 عينة من البيانات:`);
            console.log(`العنوان: ${data.title.substring(0, 50)}...`);
            if (data.description) {
                console.log(`الوصف: ${data.description.substring(0, 100)}...`);
            }
            if (data.images.length > 0) {
                console.log(`أول صورة: ${data.images[0]}`);
            }
            if (data.features.length > 0) {
                console.log(`أول مواصفة: ${data.features[0]}`);
            }
        }
        
    } catch (error) {
        const totalTime = Date.now() - startTime;
        console.error('\n❌ خطأ في الاختبار:', error.message);
        console.log(`⏱️  الوقت حتى الخطأ: ${totalTime}ms`);
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testFastExtraction();
}
