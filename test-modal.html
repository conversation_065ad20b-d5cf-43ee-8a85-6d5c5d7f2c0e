<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار Modal الصور</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(5px);
        }
        
        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            margin: auto;
            animation: modalZoom 0.3s ease-out;
        }
        
        @keyframes modalZoom {
            from { transform: scale(0.7); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }
        
        .modal-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }
        
        .modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }
        
        .modal-close:hover {
            background: rgba(255, 0, 0, 0.7);
            transform: scale(1.1);
        }
        
        .modal-download {
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .modal-download:hover {
            background: #2563eb;
            transform: translateX(-50%) scale(1.05);
        }
        
        .image-thumbnail {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }
        
        .image-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }
        
        .image-thumbnail::after {
            content: '🔍';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-size: 16px;
        }
        
        .image-thumbnail:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8">🖼️ اختبار Modal الصور</h1>
        
        <div class="bg-white rounded-lg shadow-md p-6">
            <h2 class="text-xl font-semibold mb-4">صور تجريبية</h2>
            <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4" id="testImages">
                <!-- سيتم إضافة الصور هنا بواسطة JavaScript -->
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="صورة المنتج">
            <a id="modalDownload" class="modal-download" href="" download="">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                تحميل الصورة
            </a>
        </div>
    </div>

    <script>
        // Image Modal Functions
        function openImageModal(imageSrc, imageAlt) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalDownload = document.getElementById('modalDownload');
            
            modalImage.src = imageSrc;
            modalImage.alt = imageAlt || 'صورة المنتج';
            
            modalDownload.href = imageSrc;
            modalDownload.download = `test-image-${Date.now()}.jpg`;
            
            modal.classList.add('show');
            document.body.style.overflow = 'hidden';
        }
        
        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto';
        }
        
        // إغلاق Modal عند النقر خارج الصورة
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });
        
        // إغلاق Modal بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        // إضافة صور تجريبية
        const testImageUrls = [
            'https://s.alicdn.com/@sc04/kf/Hca95eb083e4a4a4f8e56b66bbb91edb8l.jpg_400x400.jpg',
            'https://s.alicdn.com/@sc04/kf/H1e77c7eb5a1f46d999deb68e3eb18b7aY.jpg_400x400.jpg',
            'https://s.alicdn.com/@sc04/kf/H856bd70c627e4485aa191229f956e4dd1.jpg_400x400.jpg',
            'https://s.alicdn.com/@sc04/kf/Haf2a49f4166e41bf84c0642cc0ad14763.jpg_400x400.jpg'
        ];

        const container = document.getElementById('testImages');
        testImageUrls.forEach((imageUrl, index) => {
            const imageContainer = document.createElement('div');
            imageContainer.className = 'relative group';
            
            const img = document.createElement('img');
            img.src = imageUrl;
            img.alt = `صورة تجريبية ${index + 1}`;
            img.className = 'image-thumbnail w-full h-32 object-cover rounded-lg shadow-sm hover:shadow-md transition-all duration-200';
            
            img.onclick = function() {
                openImageModal(imageUrl, `صورة تجريبية ${index + 1}`);
            };
            
            const overlay = document.createElement('div');
            overlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center';
            overlay.innerHTML = '<span class="text-white font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-200">انقر للعرض</span>';
            
            imageContainer.appendChild(img);
            imageContainer.appendChild(overlay);
            container.appendChild(imageContainer);
        });
    </script>
</body>
</html>
