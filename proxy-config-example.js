// مثال على كيفية إعداد البروكسيات
// انسخ هذا الملف إلى proxy-config.js وأضف بروكسياتك الحقيقية

const ProxyManager = require('./proxy-manager');

function setupProxies() {
    const proxyManager = new ProxyManager();

    // مثال 1: إضافة بروكسي واحد
    // proxyManager.addProxy('proxy-server.com', 8080, 'username', 'password');

    // مثال 2: إضافة بروكسيات متعددة
    const proxyList = [
        // تنسيق: 'username:password@host:port'
        // 'user1:<EMAIL>:8080',
        // 'user2:<EMAIL>:8080',
        // 'user3:<EMAIL>:8080',
        
        // أو تنسيق كائن
        // { host: 'proxy1.com', port: 8080, username: 'user1', password: 'pass1' },
        // { host: 'proxy2.com', port: 8080, username: 'user2', password: 'pass2' },
    ];

    // إضافة القائمة
    // proxyManager.addProxiesFromList(proxyList);

    // مثال 3: بروكسيات مجانية (قد لا تكون موثوقة)
    const freeProxies = [
        // ملاحظة: البروكسيات المجانية قد تكون بطيئة أو غير مستقرة
        // يُنصح باستخدام بروكسيات مدفوعة للحصول على أفضل النتائج
        
        // مثال على بروكسيات مجانية (تحتاج للتحقق من صحتها)
        // '***************:7492',
        // '***************:7300',
        // '**************:8382',
    ];

    // إضافة البروكسيات المجانية
    // proxyManager.addProxiesFromList(freeProxies);

    return proxyManager;
}

// مثال على كيفية استخدام البروكسيات في الكود الرئيسي
function exampleUsage() {
    const proxyManager = setupProxies();
    
    // الحصول على بروكسي
    const proxy = proxyManager.getNextProxy();
    if (proxy) {
        console.log(`Using proxy: ${proxy.host}:${proxy.port}`);
        
        // تحويل إلى تنسيق Playwright
        const playwrightProxy = proxyManager.formatProxyForPlaywright(proxy);
        console.log('Playwright proxy config:', playwrightProxy);
    } else {
        console.log('No proxies available');
    }
    
    // عرض حالة البروكسيات
    console.log('Proxy status:', proxyManager.getStatus());
}

// تصدير الدالة
module.exports = { setupProxies, exampleUsage };

// تشغيل المثال إذا تم تشغيل الملف مباشرة
if (require.main === module) {
    exampleUsage();
}

/* 
نصائح لاستخدام البروكسيات:

1. البروكسيات المدفوعة:
   - أكثر موثوقية واستقراراً
   - سرعة أعلى
   - دعم فني أفضل
   - مثال: Bright Data, Oxylabs, ProxyMesh

2. البروكسيات المجانية:
   - قد تكون بطيئة
   - قد تنقطع بشكل متكرر
   - قد تكون غير آمنة
   - استخدمها للاختبار فقط

3. أنواع البروكسيات:
   - HTTP/HTTPS: الأكثر شيوعاً
   - SOCKS5: أكثر أماناً وسرعة
   - Residential: عناوين IP حقيقية
   - Datacenter: أسرع ولكن أسهل في الكشف

4. نصائح الأمان:
   - لا تستخدم بروكسيات مجهولة المصدر
   - تأكد من تشفير الاتصال
   - راقب استخدام البيانات
   - استخدم بروكسيات من مصادر موثوقة

5. تحسين الأداء:
   - اختبر البروكسيات قبل الاستخدام
   - استخدم بروكسيات قريبة جغرافياً
   - راقب معدل النجاح
   - قم بتدوير البروكسيات بانتظام
*/
