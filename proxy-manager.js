// Proxy Manager for rotating proxies
class ProxyManager {
    constructor() {
        // قائمة البروكسيات المجانية - يمكنك إضافة المزيد أو استخدام خدمة مدفوعة
        this.proxies = [
            // يمكنك إضافة بروكسيات مجانية هنا
            // مثال: { host: '123.456.789.0', port: 8080, username: '', password: '' }
            // أو استخدام بروكسيات مدفوعة للحصول على أداء أفضل
        ];
        
        this.currentIndex = 0;
        this.failedProxies = new Set();
    }

    // إضافة بروكسي جديد
    addProxy(host, port, username = '', password = '') {
        this.proxies.push({ host, port, username, password });
    }

    // إضافة قائمة بروكسيات من ملف أو API
    addProxiesFromList(proxyList) {
        proxyList.forEach(proxy => {
            if (typeof proxy === 'string') {
                // تحليل البروكسي من النص: "host:port" أو "username:password@host:port"
                const parts = proxy.split('@');
                let host, port, username = '', password = '';
                
                if (parts.length === 2) {
                    [username, password] = parts[0].split(':');
                    [host, port] = parts[1].split(':');
                } else {
                    [host, port] = parts[0].split(':');
                }
                
                this.addProxy(host, parseInt(port), username, password);
            } else {
                this.addProxy(proxy.host, proxy.port, proxy.username, proxy.password);
            }
        });
    }

    // الحصول على البروكسي التالي
    getNextProxy() {
        if (this.proxies.length === 0) {
            return null;
        }

        // تجاهل البروكسيات المعطلة
        let attempts = 0;
        while (attempts < this.proxies.length) {
            const proxy = this.proxies[this.currentIndex];
            const proxyKey = `${proxy.host}:${proxy.port}`;
            
            this.currentIndex = (this.currentIndex + 1) % this.proxies.length;
            
            if (!this.failedProxies.has(proxyKey)) {
                return proxy;
            }
            
            attempts++;
        }

        // إذا فشلت جميع البروكسيات، أعد تعيين القائمة
        this.failedProxies.clear();
        return this.proxies[0];
    }

    // تسجيل فشل البروكسي
    markProxyAsFailed(proxy) {
        const proxyKey = `${proxy.host}:${proxy.port}`;
        this.failedProxies.add(proxyKey);
        console.log(`Proxy ${proxyKey} marked as failed`);
    }

    // إعادة تعيين البروكسيات المعطلة
    resetFailedProxies() {
        this.failedProxies.clear();
    }

    // تحويل البروكسي إلى تنسيق Playwright
    formatProxyForPlaywright(proxy) {
        if (!proxy) return null;

        const proxyConfig = {
            server: `http://${proxy.host}:${proxy.port}`
        };

        if (proxy.username && proxy.password) {
            proxyConfig.username = proxy.username;
            proxyConfig.password = proxy.password;
        }

        return proxyConfig;
    }

    // الحصول على عدد البروكسيات المتاحة
    getAvailableProxyCount() {
        return this.proxies.length - this.failedProxies.size;
    }

    // طباعة حالة البروكسيات
    getStatus() {
        return {
            total: this.proxies.length,
            failed: this.failedProxies.size,
            available: this.getAvailableProxyCount(),
            current: this.currentIndex
        };
    }
}

module.exports = ProxyManager;
