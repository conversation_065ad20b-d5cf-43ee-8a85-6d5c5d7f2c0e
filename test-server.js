// اختبار بسيط للخادم
const express = require('express');
const app = express();
const PORT = 3004;

app.get('/', (req, res) => {
    res.send(`
        <html>
        <head>
            <title>اختبار الخادم</title>
            <meta charset="UTF-8">
        </head>
        <body style="font-family: Arial; text-align: center; padding: 50px;">
            <h1>✅ الخادم يعمل بنجاح!</h1>
            <p>الوقت الحالي: ${new Date().toLocaleString('ar-SA')}</p>
            <p>عنوان IP: ${req.ip}</p>
            <p>User-Agent: ${req.get('User-Agent')}</p>
            <hr>
            <p><a href="http://localhost:3003">الانتقال إلى التطبيق الرئيسي</a></p>
        </body>
        </html>
    `);
});

app.listen(PORT, '0.0.0.0', () => {
    console.log(`🧪 Test server running on http://0.0.0.0:${PORT}`);
    console.log(`📱 Access from: http://localhost:${PORT}`);
    console.log(`🌐 External IP: http://**************:${PORT}`);
});
