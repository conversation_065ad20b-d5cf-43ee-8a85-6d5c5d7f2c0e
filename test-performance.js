// اختبار أداء سريع للتطبيق
const { chromium } = require('playwright');

async function testPerformance() {
    console.log('🧪 بدء اختبار الأداء...');
    const startTime = Date.now();
    
    try {
        // إعدادات مبسطة للسرعة
        const browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-blink-features=AutomationControlled'
            ]
        });
        
        const context = await browser.newContext({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        });
        
        const page = await context.newPage();
        
        // إخفاء webdriver
        await page.addInitScript(() => {
            Object.defineProperty(navigator, 'webdriver', {
                get: () => undefined,
            });
        });
        
        console.log('⏱️  جاري تحميل صفحة Alibaba...');
        const navigationStart = Date.now();
        
        // اختبار مع رابط Alibaba حقيقي
        await page.goto('https://www.alibaba.com/product-detail/Custom-Logo-Wireless-Bluetooth-Earphones_1600000000000.html', {
            waitUntil: 'domcontentloaded',
            timeout: 30000
        });
        
        const navigationEnd = Date.now();
        console.log(`✅ تم تحميل الصفحة في: ${navigationEnd - navigationStart}ms`);
        
        // اختبار استخراج بسيط
        const extractionStart = Date.now();
        
        const title = await page.evaluate(() => {
            const titleSelectors = [
                'h1[data-role="titleBox"]',
                '.product-title h1',
                'h1',
                '.title h1'
            ];
            
            for (const selector of titleSelectors) {
                const element = document.querySelector(selector);
                if (element && element.textContent.trim()) {
                    return element.textContent.trim();
                }
            }
            return 'لم يتم العثور على العنوان';
        });
        
        const extractionEnd = Date.now();
        console.log(`✅ تم استخراج العنوان في: ${extractionEnd - extractionStart}ms`);
        console.log(`📝 العنوان: ${title.substring(0, 50)}...`);
        
        await browser.close();
        
        const totalTime = Date.now() - startTime;
        console.log(`🎉 إجمالي الوقت: ${totalTime}ms`);
        
        if (totalTime < 15000) {
            console.log('🚀 الأداء ممتاز! (أقل من 15 ثانية)');
        } else if (totalTime < 30000) {
            console.log('✅ الأداء جيد (أقل من 30 ثانية)');
        } else {
            console.log('⚠️  الأداء بطيء (أكثر من 30 ثانية)');
        }
        
    } catch (error) {
        console.error('❌ خطأ في الاختبار:', error.message);
        const totalTime = Date.now() - startTime;
        console.log(`⏱️  الوقت حتى الخطأ: ${totalTime}ms`);
    }
}

// تشغيل الاختبار
if (require.main === module) {
    testPerformance();
}
