# الاستخدام المتقدم - مستخرج بيانات Alibaba

## 🔧 إعداد البروكسيات

### 1. إنشاء ملف إعداد البروكسيات

```bash
cp proxy-config-example.js proxy-config.js
```

### 2. تحرير ملف البروكسيات

```javascript
// proxy-config.js
const ProxyManager = require('./proxy-manager');

function setupProxies() {
    const proxyManager = new ProxyManager();
    
    // إضافة بروكسياتك هنا
    proxyManager.addProxy('your-proxy.com', 8080, 'username', 'password');
    
    return proxyManager;
}

module.exports = { setupProxies };
```

### 3. استخدام البروكسيات في الكود الرئيسي

```javascript
// في index.js
const { setupProxies } = require('./proxy-config');

// في دالة extract-product
const proxyManager = setupProxies();
```

## 🥷 تحسينات Stealth

### الميزات المدمجة:
- إخفاء خاصية `navigator.webdriver`
- تعديل `navigator.plugins`
- إخفاء `chrome.runtime`
- تحسين `navigator.languages`
- تعديل permissions API

### إضافة تحسينات إضافية:

```javascript
await page.addInitScript(() => {
    // إخفاء canvas fingerprinting
    const getContext = HTMLCanvasElement.prototype.getContext;
    HTMLCanvasElement.prototype.getContext = function(type) {
        if (type === '2d') {
            const context = getContext.call(this, type);
            const getImageData = context.getImageData;
            context.getImageData = function(x, y, w, h) {
                const imageData = getImageData.call(this, x, y, w, h);
                for (let i = 0; i < imageData.data.length; i += 4) {
                    imageData.data[i] += Math.floor(Math.random() * 10) - 5;
                    imageData.data[i + 1] += Math.floor(Math.random() * 10) - 5;
                    imageData.data[i + 2] += Math.floor(Math.random() * 10) - 5;
                }
                return imageData;
            };
            return context;
        }
        return getContext.call(this, type);
    };
});
```

## 🔄 إدارة البروكسيات المتقدمة

### 1. اختبار البروكسيات

```javascript
async function testProxy(proxy) {
    try {
        const browser = await chromium.launch();
        const context = await browser.newContext({
            proxy: proxyManager.formatProxyForPlaywright(proxy)
        });
        const page = await context.newPage();
        
        await page.goto('https://httpbin.org/ip', { timeout: 10000 });
        const response = await page.textContent('body');
        const data = JSON.parse(response);
        
        console.log(`Proxy ${proxy.host}:${proxy.port} - IP: ${data.origin}`);
        
        await browser.close();
        return true;
    } catch (error) {
        console.log(`Proxy ${proxy.host}:${proxy.port} failed: ${error.message}`);
        return false;
    }
}
```

### 2. تحميل البروكسيات من ملف

```javascript
const fs = require('fs');

function loadProxiesFromFile(filename) {
    try {
        const data = fs.readFileSync(filename, 'utf8');
        const lines = data.split('\n').filter(line => line.trim());
        
        const proxies = lines.map(line => {
            const [host, port] = line.split(':');
            return { host: host.trim(), port: parseInt(port.trim()) };
        });
        
        return proxies;
    } catch (error) {
        console.error('Error loading proxies:', error);
        return [];
    }
}
```

### 3. دوران البروكسيات الذكي

```javascript
class SmartProxyManager extends ProxyManager {
    constructor() {
        super();
        this.proxyStats = new Map();
    }
    
    recordSuccess(proxy) {
        const key = `${proxy.host}:${proxy.port}`;
        const stats = this.proxyStats.get(key) || { success: 0, failure: 0 };
        stats.success++;
        this.proxyStats.set(key, stats);
    }
    
    recordFailure(proxy) {
        const key = `${proxy.host}:${proxy.port}`;
        const stats = this.proxyStats.get(key) || { success: 0, failure: 0 };
        stats.failure++;
        this.proxyStats.set(key, stats);
        
        // إذا فشل البروكسي أكثر من 3 مرات، اعتبره معطل
        if (stats.failure > 3) {
            this.markProxyAsFailed(proxy);
        }
    }
    
    getBestProxy() {
        const availableProxies = this.proxies.filter(proxy => {
            const key = `${proxy.host}:${proxy.port}`;
            return !this.failedProxies.has(key);
        });
        
        if (availableProxies.length === 0) return null;
        
        // اختر البروكسي بأعلى معدل نجاح
        return availableProxies.sort((a, b) => {
            const statsA = this.proxyStats.get(`${a.host}:${a.port}`) || { success: 0, failure: 0 };
            const statsB = this.proxyStats.get(`${b.host}:${b.port}`) || { success: 0, failure: 0 };
            
            const rateA = statsA.success / (statsA.success + statsA.failure + 1);
            const rateB = statsB.success / (statsB.success + statsB.failure + 1);
            
            return rateB - rateA;
        })[0];
    }
}
```

## 📊 مراقبة الأداء

### 1. إضافة مقاييس الأداء

```javascript
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            proxyUsage: new Map()
        };
    }
    
    recordRequest(proxy, success, responseTime) {
        this.metrics.totalRequests++;
        
        if (success) {
            this.metrics.successfulRequests++;
        } else {
            this.metrics.failedRequests++;
        }
        
        // حساب متوسط وقت الاستجابة
        this.metrics.averageResponseTime = 
            (this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime) 
            / this.metrics.totalRequests;
        
        // تسجيل استخدام البروكسي
        if (proxy) {
            const key = `${proxy.host}:${proxy.port}`;
            const usage = this.metrics.proxyUsage.get(key) || 0;
            this.metrics.proxyUsage.set(key, usage + 1);
        }
    }
    
    getReport() {
        const successRate = (this.metrics.successfulRequests / this.metrics.totalRequests * 100).toFixed(2);
        
        return {
            ...this.metrics,
            successRate: `${successRate}%`,
            topProxies: Array.from(this.metrics.proxyUsage.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 5)
        };
    }
}
```

## 🛡️ الأمان والخصوصية

### 1. تشفير بيانات البروكسي

```javascript
const crypto = require('crypto');

function encryptProxyData(data, key) {
    const cipher = crypto.createCipher('aes192', key);
    let encrypted = cipher.update(JSON.stringify(data), 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
}

function decryptProxyData(encryptedData, key) {
    const decipher = crypto.createDecipher('aes192', key);
    let decrypted = decipher.update(encryptedData, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    return JSON.parse(decrypted);
}
```

### 2. تنظيف البيانات الحساسة

```javascript
process.on('exit', () => {
    // مسح البيانات الحساسة من الذاكرة
    if (global.proxyCredentials) {
        global.proxyCredentials = null;
    }
});
```

## 🚀 تحسين الأداء

### 1. تجميع الطلبات

```javascript
class RequestBatcher {
    constructor(batchSize = 5, delay = 1000) {
        this.batchSize = batchSize;
        this.delay = delay;
        this.queue = [];
        this.processing = false;
    }
    
    async addRequest(url, options = {}) {
        return new Promise((resolve, reject) => {
            this.queue.push({ url, options, resolve, reject });
            this.processBatch();
        });
    }
    
    async processBatch() {
        if (this.processing || this.queue.length === 0) return;
        
        this.processing = true;
        
        while (this.queue.length > 0) {
            const batch = this.queue.splice(0, this.batchSize);
            
            await Promise.all(batch.map(async (request) => {
                try {
                    const result = await this.executeRequest(request.url, request.options);
                    request.resolve(result);
                } catch (error) {
                    request.reject(error);
                }
            }));
            
            if (this.queue.length > 0) {
                await new Promise(resolve => setTimeout(resolve, this.delay));
            }
        }
        
        this.processing = false;
    }
}
```

## 📝 نصائح للاستخدام الأمثل

1. **استخدم بروكسيات متنوعة**: امزج بين بروكسيات من مناطق جغرافية مختلفة
2. **راقب معدل الطلبات**: لا تفرط في الطلبات لتجنب الحظر
3. **اختبر البروكسيات دورياً**: تأكد من عمل البروكسيات بانتظام
4. **احتفظ بنسخة احتياطية**: احتفظ بقائمة بروكسيات احتياطية
5. **استخدم User-Agents متنوعة**: غير User-Agent بانتظام
6. **راقب الأخطاء**: تتبع الأخطاء وحللها لتحسين الأداء
