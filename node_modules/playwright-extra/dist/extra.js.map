{"version": 3, "file": "extra.js", "sourceRoot": "", "sources": ["../src/extra.ts"], "names": [], "mappings": ";;;;;;AAAA,kDAAyB;AACzB,MAAM,KAAK,GAAG,IAAA,eAAK,EAAC,kBAAkB,CAAC,CAAA;AAKvC,uCAAsC;AACtC,4CAAkD;AAclD;;GAEG;AACH,MAAa,oBAAoB;IAI/B,YAAoB,SAA8C;QAA9C,cAAS,GAAT,SAAS,CAAqC;QAChE,IAAI,CAAC,OAAO,GAAG,IAAI,oBAAU,EAAE,CAAA;IACjC,CAAC;IAED;;;;;;;;;;;;;;OAcG;IACI,GAAG,CAAC,MAAwB;QACjC,MAAM,OAAO,GAAG,MAAM,IAAI,MAAM,IAAI,MAAM,CAAA;QAC1C,IAAI,CAAC,OAAO,EAAE;YACZ,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACvD;QACD,IAAI,IAAI,CAAC,OAAO,CAAC,GAAG,CAAC,MAAgB,CAAC,EAAE;YACtC,KAAK,CAAC,mBAAmB,EAAE,MAAM,CAAC,IAAI,CAAC,CAAA;SACxC;QACD,OAAO,IAAI,CAAA;IACb,CAAC;IAED;;;;;;;;;OASG;IACH,IAAW,QAAQ;QACjB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACnB,MAAM,yBAAgB,CAAC,YAAY,CAAA;SACpC;QACD,OAAO,IAAI,CAAC,SAAS,CAAA;IACvB,CAAC;IAEM,KAAK,CAAC,MAAM,CACjB,GAAG,IAAqD;QAExD,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE;YACzB,MAAM,IAAI,KAAK,CAAC,oCAAoC,CAAC,CAAA;SACtD;QAED,IAAI,CAAC,OAAO,CAAC,GAAG,IAAI,CAAA;QACpB,OAAO,mBAAK,IAAI,EAAE,EAAE,IAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAE,CAAA,CAAC,wBAAwB;QACnE,KAAK,CAAC,QAAQ,EAAE,OAAO,CAAC,CAAA;QACxB,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;QAEtB,kEAAkE;QAClE,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,IAAI,OAAO,CAAA;QAE3E,KAAK,CAAC,qBAAqB,EAAE,OAAO,CAAC,CAAA;QACrC,IAAI,aAAa,IAAI,OAAO,EAAE;YAC5B,KAAK,CACH,6FAA6F,CAC9F,CAAA;YACD,OAAQ,OAAe,CAAC,WAAW,CAAA;SACpC;QACD,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC,OAAO,CAAC,CAAA;QACtD,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAC3D,OAAO,OAAO,CAAA;IAChB,CAAC;IAEM,KAAK,CAAC,uBAAuB,CAClC,GAAG,IAAsE;QAEzE,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,uBAAuB,EAAE;YAC1C,MAAM,IAAI,KAAK,CAAC,qDAAqD,CAAC,CAAA;SACvE;QAED,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,GAAG,IAAI,CAAA;QACjC,OAAO,mBAAK,IAAI,EAAE,EAAE,IAAK,CAAC,OAAO,IAAI,EAAE,CAAC,CAAE,CAAA,CAAC,wBAAwB;QACnE,KAAK,CAAC,yBAAyB,EAAE,OAAO,CAAC,CAAA;QACzC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;QAEtB,kEAAkE;QAClE,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,IAAI,OAAO,CAAA;QAE3E,MAAM,OAAO,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,yBAAyB,CAAC,CAC5D,WAAW,EACX,OAAO,CACR,CAAA;QACD,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,aAAa,EAAE,OAAO,CAAC,CAAA;QAC3D,IAAI,CAAC,yBAAyB,CAAC,OAAO,CAAC,CAAA;QACvC,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,KAAK,CAAC,OAAO,CACX,mBAA2E,EAC3E,YAA+B,EAAE;QAEjC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YAC1B,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAA;SACvD;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;QAEtB,qEAAqE;QACrE,IAAI,OAAO,GAAgD,EAAE,CAAA;QAC7D,IAAI,kBAAkB,GAAG,KAAK,CAAA;QAC9B,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;YAC3C,OAAO,mCAAQ,mBAAmB,GAAK,SAAS,CAAE,CAAA;SACnD;aAAM;YACL,kBAAkB,GAAG,IAAI,CAAA;YACzB,OAAO,mBAAK,UAAU,EAAE,mBAAmB,IAAK,SAAS,CAAE,CAAA;SAC5D;QACD,KAAK,CAAC,SAAS,EAAE,OAAO,CAAC,CAAA;QAEzB,sEAAsE;QACtE,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,IAAI,OAAO,CAAA;QAE5E,oCAAoC;QACpC,MAAM,IAAI,GAAU,EAAE,CAAA;QACtB,MAAM,UAAU,GAAG,OAAO,CAAC,UAAU,CAAA;QACrC,IAAI,kBAAkB,EAAE;YACtB,OAAO,OAAO,CAAC,UAAU,CAAA;YACzB,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;SAC/B;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACnB;QAED,MAAM,OAAO,GAAG,CAAC,MAAO,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAS,CACtD,GAAG,IAAI,CACR,CAAe,CAAA;QAEhB,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,OAAO,CAAA;IAChB,CAAC;IAED,KAAK,CAAC,cAAc,CAClB,mBAEyD,EACzD,YAAsC,EAAE;QAExC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,cAAc,EAAE;YACjC,MAAM,IAAI,KAAK,CAAC,8CAA8C,CAAC,CAAA;SAChE;QACD,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,CAAA;QAEtB,4EAA4E;QAC5E,IAAI,OAAO,GAAwD,EAAE,CAAA;QACrE,IAAI,kBAAkB,GAAG,KAAK,CAAA;QAC9B,IAAI,OAAO,mBAAmB,KAAK,QAAQ,EAAE;YAC3C,OAAO,mCAAQ,mBAAmB,GAAK,SAAS,CAAE,CAAA;SACnD;aAAM;YACL,kBAAkB,GAAG,IAAI,CAAA;YACzB,OAAO,mBAAK,WAAW,EAAE,mBAAmB,IAAK,SAAS,CAAE,CAAA;SAC7D;QACD,KAAK,CAAC,gBAAgB,CAAC,EAAE,OAAO,CAAA;QAEhC,sEAAsE;QACtE,OAAO;YACL,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC,IAAI,OAAO,CAAA;QAE5E,oCAAoC;QACpC,MAAM,IAAI,GAAU,EAAE,CAAA;QACtB,MAAM,WAAW,GAAG,OAAO,CAAC,WAAW,CAAA;QACvC,IAAI,kBAAkB,EAAE;YACtB,OAAO,OAAO,CAAC,WAAW,CAAA;YAC1B,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;SAChC;aAAM;YACL,IAAI,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA;SACnB;QAED,MAAM,OAAO,GAAG,CAAC,MAAO,IAAI,CAAC,QAAQ,CAAC,gBAAgB,CAAS,CAC7D,GAAG,IAAI,CACR,CAAe,CAAA;QAEhB,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,WAAW,EAAE,OAAO,CAAC,CAAA;QACzD,MAAM,IAAI,CAAC,kBAAkB,CAAC,OAAO,CAAC,CAAA;QACtC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAA;QAC5D,OAAO,OAAO,CAAA;IAChB,CAAC;IAES,KAAK,CAAC,yBAAyB,CACvC,OAA0B,EAC1B,cAAyC;QAEzC,KAAK,CAAC,2BAA2B,CAAC,CAAA;QAClC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,kBAAkB,EAAE,OAAO,EAAE,cAAc,CAAC,CAAA;QAElE,sFAAsF;QACtF,OAAO,CAAC,OAAO,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE;YACzC,OAAO,KAAK,IAAI,EAAE;gBAChB,MAAM,IAAI,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,GAAG,CAAC,CAAA;gBAC3C,MAAM,IAAI,CAAC,IAAI,CAAC,aAAa,CAAC,CAAA;gBAC9B,OAAO,IAAI,CAAA;YACb,CAAC,CAAA;QACH,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAA;QAE5B,OAAO,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;YACvB,sFAAsF;YACtF,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,EAAE;gBACtB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,CAAC,CAAA;aACxC;QACH,CAAC,CAAC,CAAA;QACF,OAAO,CAAC,EAAE,CAAC,MAAM,EAAE,IAAI,CAAC,EAAE;YACxB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,eAAe,EAAE,IAAI,CAAC,CAAA;YAC5C,IAAI,CAAC,EAAE,CAAC,OAAO,EAAE,GAAG,EAAE;gBACpB,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,aAAa,EAAE,IAAI,CAAC,CAAA;YAC5C,CAAC,CAAC,CAAA;QACJ,CAAC,CAAC,CAAA;IACJ,CAAC;IAES,KAAK,CAAC,kBAAkB,CAAC,OAAmB;QACpD,KAAK,CAAC,8BAA8B,CAAC,CAAA;QAErC,OAAO,CAAC,EAAE,CAAC,cAAc,EAAE,GAAG,EAAE;YAC9B,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,gBAAgB,EAAE,OAAO,CAAC,CAAA;QAClD,CAAC,CAAC,CAAA;QAEF,4EAA4E;QAC5E,OAAO,CAAC,UAAU,GAAG,CAAC,CAAC,cAAc,EAAE,GAAG,EAAE,EAAE;YAC5C,OAAO,KAAK,EAAE,UAAoC,EAAE,EAAE,EAAE;gBACtD,MAAM,cAAc,GAClB,CAAC,MAAM,IAAI,CAAC,OAAO,CAAC,gBAAgB,CAClC,eAAe,EACf,OAAO,EACP,OAAO,CACR,CAAC,IAAI,OAAO,CAAA;gBACf,MAAM,OAAO,GAAG,MAAM,cAAc,CAAC,IAAI,CAAC,GAAG,EAAE,cAAc,CAAC,CAAA;gBAC9D,IAAI,CAAC,yBAAyB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAA;gBACvD,OAAO,OAAO,CAAA;YAChB,CAAC,CAAA;QACH,CAAC,CAAC,CAAC,OAAO,CAAC,UAAU,EAAE,OAAO,CAAC,CAAA;IACjC,CAAC;CACF;AAxPD,oDAwPC;AAED;;;;;GAKG;AACU,QAAA,eAAe,GAAG,IAAI,KAAK,CAAC,oBAAoB,EAAE;IAC7D,SAAS,CAAC,WAAW,EAAE,IAAI;QACzB,KAAK,CAAC,sBAAsB,WAAW,CAAC,IAAI,EAAE,CAAC,CAAA;QAC/C,MAAM,MAAM,GAAG,OAAO,CAAC,SAAS,CAAC,WAAW,EAAE,IAAI,CAAyB,CAAA;QAC3E,OAAO,IAAI,KAAK,CAAC,MAAM,EAAE;YACvB,GAAG,CAAC,MAAM,EAAE,IAAI;gBACd,IAAI,IAAI,IAAI,MAAM,EAAE;oBAClB,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAA;iBACjC;gBACD,KAAK,CAAC,0CAA0C,EAAE,IAAI,CAAC,CAAA;gBACvD,OAAO,OAAO,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAA;YAC3C,CAAC;SACF,CAAC,CAAA;IACJ,CAAC;CACF,CAAC,CAAA"}