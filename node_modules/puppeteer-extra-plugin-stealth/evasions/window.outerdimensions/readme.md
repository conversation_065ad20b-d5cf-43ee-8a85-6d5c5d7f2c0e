## API

<!-- Generated by documentation.js. Update this documentation by updating the source code. -->

#### Table of Contents

- [class: Plugin](#class-plugin)

### class: [Plugin](https://github.com/berstend/puppeteer-extra/blob/e6133619b051febed630ada35241664eba59b9fa/packages/puppeteer-extra-plugin-stealth/evasions/window.outerdimensions/index.js#L9-L40)

- `opts` (optional, default `{}`)

**Extends: PuppeteerExtraPlugin**

Fix missing window.outerWidth/window.outerHeight in headless mode
Will also set the viewport to match window size, unless specified by user

---
