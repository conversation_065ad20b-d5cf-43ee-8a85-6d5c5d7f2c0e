{"name": "puppeteer-extra-plugin", "version": "3.2.3", "description": "Base class for puppeteer-extra plugins.", "main": "dist/index.cjs.js", "module": "dist/index.esm.js", "typings": "dist/index.d.ts", "files": ["dist"], "repository": "berstend/puppeteer-extra", "homepage": "https://github.com/berstend/puppeteer-extra/tree/master/packages/puppeteer-extra-plugin", "author": "be<PERSON><PERSON>", "license": "MIT", "scripts": {"clean": "rimraf dist/*", "prebuild": "run-s clean", "build": "run-s build:tsc build:rollup", "build:tsc": "tsc --module commonjs", "build:rollup": "rollup -c rollup.config.ts", "docs": "documentation readme --quiet --shallow --github --markdown-theme transitivebs --readme-file readme.md --section API ./src/index.ts", "postdocs": "npx prettier --write readme.md", "test": "ava -v --config ava.config-ts.js", "pretest-ci": "run-s build", "test-ci": "ava --fail-fast -v"}, "engines": {"node": ">=9.11.2"}, "prettier": {"printWidth": 80, "semi": false, "singleQuote": true}, "keywords": ["puppeteer", "puppeteer-extra", "puppeteer-extra-plugin", "ua", "user-agent", "chrome", "headless", "pupeteer"], "devDependencies": {"@types/node": "14.14.34", "@types/puppeteer": "*", "ava": "2.4.0", "documentation-markdown-themes": "^12.1.5", "npm-run-all": "^4.1.5", "puppeteer": "9", "rimraf": "^3.0.0", "rollup": "^1.27.5", "rollup-plugin-commonjs": "^10.1.0", "rollup-plugin-node-resolve": "^5.2.0", "rollup-plugin-sourcemaps": "^0.4.2", "rollup-plugin-typescript2": "^0.25.2", "ts-node": "^8.5.4", "tslint": "^5.12.1", "tslint-config-prettier": "^1.18.0", "tslint-config-standard": "^9.0.0", "typescript": "4.4.3"}, "dependencies": {"@types/debug": "^4.1.0", "debug": "^4.1.1", "merge-deep": "^3.0.1"}, "peerDependencies": {"playwright-extra": "*", "puppeteer-extra": "*"}, "peerDependenciesMeta": {"puppeteer-extra": {"optional": true}, "playwright-extra": {"optional": true}}, "gitHead": "2f4a357f233b35a7a20f16ce007f5ef3f62765b9"}