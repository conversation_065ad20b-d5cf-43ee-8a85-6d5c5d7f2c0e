{"comment": "Do not edit this file, use utils/roll_browser.js", "browsers": [{"name": "chromium", "revision": "1179", "installByDefault": true, "browserVersion": "138.0.7204.23"}, {"name": "chromium-headless-shell", "revision": "1179", "installByDefault": true, "browserVersion": "138.0.7204.23"}, {"name": "chromium-tip-of-tree", "revision": "1337", "installByDefault": false, "browserVersion": "139.0.7216.0"}, {"name": "chromium-tip-of-tree-headless-shell", "revision": "1337", "installByDefault": false, "browserVersion": "139.0.7216.0"}, {"name": "firefox", "revision": "1488", "installByDefault": true, "browserVersion": "139.0"}, {"name": "firefox-beta", "revision": "1482", "installByDefault": false, "browserVersion": "138.0b10"}, {"name": "webkit", "revision": "2182", "installByDefault": true, "revisionOverrides": {"debian11-x64": "2105", "debian11-arm64": "2105", "mac10.14": "1446", "mac10.15": "1616", "mac11": "1816", "mac11-arm64": "1816", "mac12": "2009", "mac12-arm64": "2009", "mac13": "2140", "mac13-arm64": "2140", "ubuntu20.04-x64": "2092", "ubuntu20.04-arm64": "2092"}, "browserVersion": "18.5"}, {"name": "ffmpeg", "revision": "1011", "installByDefault": true, "revisionOverrides": {"mac12": "1010", "mac12-arm64": "1010"}}, {"name": "winldd", "revision": "1007", "installByDefault": false}, {"name": "android", "revision": "1001", "installByDefault": false}]}