<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>مستخرج بيانات منتجات Alibaba</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .loading {
            display: none;
        }
        .loading.show {
            display: block;
        }
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 2s linear infinite;
            margin: 0 auto;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Modal Styles */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            backdrop-filter: blur(5px);
        }

        .modal.show {
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .modal-content {
            position: relative;
            max-width: 90%;
            max-height: 90%;
            margin: auto;
            animation: modalZoom 0.3s ease-out;
        }

        @keyframes modalZoom {
            from { transform: scale(0.7); opacity: 0; }
            to { transform: scale(1); opacity: 1; }
        }

        .modal-image {
            max-width: 100%;
            max-height: 80vh;
            object-fit: contain;
            border-radius: 8px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
        }

        .modal-close {
            position: absolute;
            top: -40px;
            right: 0;
            color: white;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            background: rgba(0, 0, 0, 0.5);
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 0, 0, 0.7);
            transform: scale(1.1);
        }

        .modal-download {
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            background: #3b82f6;
            color: white;
            padding: 10px 20px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: bold;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .modal-download:hover {
            background: #2563eb;
            transform: translateX(-50%) scale(1.05);
        }

        .image-thumbnail {
            cursor: pointer;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .image-thumbnail:hover {
            transform: scale(1.05);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
        }

        .image-thumbnail::after {
            content: '🔍';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 8px;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.3s ease;
            font-size: 16px;
        }

        .image-thumbnail:hover::after {
            opacity: 1;
        }
    </style>
</head>
<body class="bg-gray-100 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <div class="max-w-4xl mx-auto">
            <!-- Header -->
            <div class="text-center mb-8">
                <h1 class="text-4xl font-bold text-gray-800 mb-4">مستخرج بيانات منتجات Alibaba</h1>
                <p class="text-gray-600">أدخل رابط منتج من موقع Alibaba لاستخراج البيانات تلقائياً</p>
            </div>

            <!-- Input Form -->
            <div class="bg-white rounded-lg shadow-md p-6 mb-8">
                <form id="productForm" class="space-y-4">
                    <div>
                        <label for="productUrl" class="block text-sm font-medium text-gray-700 mb-2">
                            رابط المنتج من Alibaba
                        </label>
                        <input 
                            type="url" 
                            id="productUrl" 
                            name="productUrl" 
                            placeholder="https://www.alibaba.com/product-detail/..."
                            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            required
                        >
                    </div>
                    <button 
                        type="submit" 
                        class="w-full bg-blue-600 text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition duration-200"
                    >
                        استخراج البيانات
                    </button>
                </form>
            </div>

            <!-- Loading Indicator with Progress Bar -->
            <div id="loading" class="loading text-center py-8">
                <div class="max-w-md mx-auto">
                    <div class="spinner mb-4"></div>
                    <div class="bg-gray-200 rounded-full h-3 mb-4">
                        <div id="progressBar" class="bg-blue-600 h-3 rounded-full transition-all duration-500 ease-out" style="width: 0%"></div>
                    </div>
                    <p id="loadingText" class="text-gray-600 font-medium">جاري تحضير المتصفح...</p>
                    <p id="loadingSubtext" class="text-sm text-gray-500 mt-2">قد يستغرق هذا 10-20 ثانية</p>
                </div>
            </div>

            <!-- Error Message -->
            <div id="errorMessage" class="hidden bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
                <strong>خطأ:</strong> <span id="errorText"></span>
            </div>

            <!-- Results Container -->
            <div id="results" class="hidden">
                <div class="bg-white rounded-lg shadow-md p-6">
                    <!-- Product Title -->
                    <h2 id="productTitle" class="text-2xl font-bold text-gray-800 mb-4"></h2>
                    
                    <!-- Product Images -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">صور المنتج</h3>
                        <div id="productImages" class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"></div>
                    </div>
                    
                    <!-- Product Description -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">وصف المنتج</h3>
                        <p id="productDescription" class="text-gray-600 leading-relaxed"></p>
                    </div>
                    
                    <!-- Product Features -->
                    <div class="mb-6">
                        <h3 class="text-lg font-semibold text-gray-700 mb-3">المواصفات والمميزات</h3>
                        <ul id="productFeatures" class="list-disc list-inside space-y-2 text-gray-600"></ul>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Image Modal -->
    <div id="imageModal" class="modal">
        <div class="modal-content">
            <span class="modal-close" onclick="closeImageModal()">&times;</span>
            <img id="modalImage" class="modal-image" src="" alt="صورة المنتج">
            <a id="modalDownload" class="modal-download" href="" download="">
                <svg width="20" height="20" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                </svg>
                تحميل الصورة
            </a>
        </div>
    </div>

    <script>
        // Image Modal Functions
        function openImageModal(imageSrc, imageAlt) {
            const modal = document.getElementById('imageModal');
            const modalImage = document.getElementById('modalImage');
            const modalDownload = document.getElementById('modalDownload');

            modalImage.src = imageSrc;
            modalImage.alt = imageAlt || 'صورة المنتج';

            // إعداد رابط التحميل
            modalDownload.href = imageSrc;
            modalDownload.download = `alibaba-product-image-${Date.now()}.jpg`;

            modal.classList.add('show');
            document.body.style.overflow = 'hidden'; // منع التمرير
        }

        function closeImageModal() {
            const modal = document.getElementById('imageModal');
            modal.classList.remove('show');
            document.body.style.overflow = 'auto'; // إعادة التمرير
        }

        // إغلاق Modal عند النقر خارج الصورة
        document.getElementById('imageModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeImageModal();
            }
        });

        // إغلاق Modal بمفتاح Escape
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                closeImageModal();
            }
        });

        document.getElementById('productForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const url = document.getElementById('productUrl').value;
            const loading = document.getElementById('loading');
            const results = document.getElementById('results');
            const errorMessage = document.getElementById('errorMessage');
            const progressBar = document.getElementById('progressBar');
            const loadingText = document.getElementById('loadingText');
            const loadingSubtext = document.getElementById('loadingSubtext');

            // Show loading, hide results and errors
            loading.classList.add('show');
            results.classList.add('hidden');
            errorMessage.classList.add('hidden');

            // Reset progress
            progressBar.style.width = '0%';
            loadingText.textContent = 'جاري تحضير المتصفح...';
            loadingSubtext.textContent = 'قد يستغرق هذا 10-20 ثانية';

            // Generate unique session ID
            const sessionId = 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);

            // Setup Server-Sent Events for real-time progress
            const eventSource = new EventSource(`/progress/${sessionId}`);

            eventSource.onmessage = function(event) {
                const data = JSON.parse(event.data);
                progressBar.style.width = data.progress + '%';
                loadingText.textContent = data.message;
                if (data.submessage) {
                    loadingSubtext.textContent = data.submessage;
                }
            };

            eventSource.onerror = function(event) {
                console.log('SSE connection error:', event);
                eventSource.close();
            };

            try {
                const response = await fetch('/extract-product', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ url: url, sessionId: sessionId })
                });

                const data = await response.json();

                if (!response.ok) {
                    throw new Error(data.error || 'حدث خطأ أثناء استخراج البيانات');
                }

                // Wait a moment before showing results
                setTimeout(() => {
                    eventSource.close();
                    loading.classList.remove('show');
                    displayResults(data);
                }, 1500);

            } catch (error) {
                eventSource.close();
                console.error('Error:', error);
                document.getElementById('errorText').textContent = error.message;
                errorMessage.classList.remove('hidden');
                loading.classList.remove('show');
            }
        });
        
        function displayResults(data) {
            // Product Title
            document.getElementById('productTitle').textContent = data.title || 'غير متوفر';
            
            // Product Images with Modal functionality
            const imagesContainer = document.getElementById('productImages');
            imagesContainer.innerHTML = '';
            if (data.images && data.images.length > 0) {
                data.images.forEach((imageUrl, index) => {
                    // إنشاء container للصورة
                    const imageContainer = document.createElement('div');
                    imageContainer.className = 'relative group';

                    const img = document.createElement('img');
                    img.src = imageUrl;
                    img.alt = `صورة المنتج ${index + 1}`;
                    img.className = 'image-thumbnail w-full h-32 object-cover rounded-lg shadow-sm hover:shadow-md transition-all duration-200';

                    // إضافة وظيفة النقر لفتح Modal
                    img.onclick = function() {
                        openImageModal(imageUrl, `صورة المنتج ${index + 1}`);
                    };

                    // معالجة خطأ تحميل الصورة
                    img.onerror = function() {
                        imageContainer.style.display = 'none';
                    };

                    // إضافة نص توضيحي
                    const overlay = document.createElement('div');
                    overlay.className = 'absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center';
                    overlay.innerHTML = '<span class="text-white font-bold opacity-0 group-hover:opacity-100 transition-opacity duration-200">انقر للعرض</span>';

                    imageContainer.appendChild(img);
                    imageContainer.appendChild(overlay);
                    imagesContainer.appendChild(imageContainer);
                });

                // إضافة عداد الصور
                const imageCount = document.createElement('p');
                imageCount.className = 'text-sm text-gray-500 mt-2 text-center col-span-full';
                imageCount.textContent = `تم العثور على ${data.images.length} صورة - انقر على أي صورة لعرضها وتحميلها`;
                imagesContainer.appendChild(imageCount);
            } else {
                imagesContainer.innerHTML = '<p class="text-gray-500 col-span-full">لا توجد صور متاحة</p>';
            }
            
            // Product Description
            document.getElementById('productDescription').textContent = data.description || 'لا يوجد وصف متاح';
            
            // Product Features
            const featuresContainer = document.getElementById('productFeatures');
            featuresContainer.innerHTML = '';
            if (data.features && data.features.length > 0) {
                data.features.forEach(feature => {
                    const li = document.createElement('li');
                    li.textContent = feature;
                    li.className = 'text-gray-600';
                    featuresContainer.appendChild(li);
                });
            } else {
                featuresContainer.innerHTML = '<li class="text-gray-500">لا توجد مواصفات متاحة</li>';
            }
            
            // Show results
            document.getElementById('results').classList.remove('hidden');
        }
    </script>
</body>
</html>
