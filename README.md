# مستخرج بيانات منتجات Alibaba - الإصدار المحسن

تطبيق ويب متقدم لاستخراج بيانات المنتجات من موقع Alibaba تلقائياً باستخدام Node.js وExpress وPlaywright مع تقنيات Stealth ودوران البروكسي.

## 🚀 التحديثات الجديدة

### ✨ التحويل من Puppeteer إلى Playwright
- **أداء محسن**: Playwright أسرع وأكثر استقراراً من Puppeteer
- **دعم أفضل للمواقع الحديثة**: تعامل محسن مع JavaScript المعقد
- **استهلاك ذاكرة أقل**: كفاءة أكبر في استخدام الموارد

### 🥷 Stealth Plugin
- **تجنب الكشف**: إخفاء آثار الأتمتة من المواقع
- **تجاوز الحماية**: تجاوز أنظمة مكافحة البوتات
- **تصفح طبيعي**: محاكاة سلوك المستخدم الحقيقي

### 🔄 دوران البروكسي (Proxy Rotation)
- **إخفاء الهوية**: استخدام عناوين IP متعددة
- **تجنب الحظر**: تقليل احتمالية حظر IP
- **موثوقية عالية**: التبديل التلقائي عند فشل البروكسي

## المميزات

- 🔍 استخراج تلقائي لبيانات المنتجات من روابط Alibaba
- 📱 واجهة مستخدم متجاوبة باستخدام Tailwind CSS
- 🖼️ عرض صور المنتج في شبكة منظمة
- 📝 استخراج عنوان ووصف المنتج
- ⚙️ استخراج المواصفات والمميزات
- 🌐 دعم اللغة العربية

## البيانات المستخرجة

- **اسم المنتج**: العنوان الرئيسي للمنتج
- **وصف المنتج**: الوصف التفصيلي
- **الصور**: مجموعة من صور المنتج عالية الجودة
- **المواصفات**: قائمة بالمميزات والمواصفات التقنية

## متطلبات التشغيل

- Node.js (الإصدار 14 أو أحدث)
- npm (يأتي مع Node.js)

## تعليمات التثبيت والتشغيل

### 1. تثبيت المكتبات المطلوبة

```bash
npm install
```

### 2. تثبيت متصفحات Playwright

```bash
npx playwright install chromium
```

### 3. إعداد البروكسيات (اختياري)

يمكنك إضافة بروكسيات في ملف `index.js`:

```javascript
// في دالة extract-product
const proxyManager = new ProxyManager();

// إضافة بروكسي واحد
proxyManager.addProxy('proxy-host', 8080, 'username', 'password');

// إضافة قائمة بروكسيات
proxyManager.addProxiesFromList([
    'username:<EMAIL>:8080',
    'username:<EMAIL>:8080'
]);
```

### 4. تشغيل الخادم

```bash
node index.js
```

### 3. فتح التطبيق

افتح المتصفح وانتقل إلى:
```
http://localhost:3003
```

## كيفية الاستخدام

1. **أدخل رابط المنتج**: انسخ رابط أي منتج من موقع Alibaba والصقه في حقل الإدخال
2. **اضغط على "استخراج البيانات"**: انتظر بضع ثوانٍ حتى يتم استخراج البيانات
3. **اعرض النتائج**: ستظهر بيانات المنتج مع الصور والمواصفات

## مثال على رابط صالح

```
https://www.alibaba.com/product-detail/Custom-Logo-Wireless-Bluetooth-Earphones_1600000000000.html
```

## هيكل المشروع

```
├── index.js              # الخادم الرئيسي
├── public/
│   └── index.html        # واجهة المستخدم
├── package.json          # إعدادات المشروع
└── README.md            # هذا الملف
```

## التقنيات المستخدمة

- **Backend**: Node.js + Express.js
- **Web Scraping**: Playwright + Stealth Plugin
- **Proxy Management**: نظام دوران البروكسي المخصص
- **Frontend**: HTML + Tailwind CSS + JavaScript
- **Styling**: Tailwind CSS من CDN
- **Security**: تقنيات إخفاء الهوية والتجسس المضاد

## ملاحظات مهمة

- ⚠️ التطبيق مخصص للاستخدام التعليمي والتجريبي فقط
- 🕐 قد يستغرق استخراج البيانات من 5-15 ثانية حسب سرعة الإنترنت
- 🔒 تأكد من احترام شروط استخدام موقع Alibaba
- 🌐 يعمل التطبيق بشكل أفضل مع الروابط الإنجليزية لموقع Alibaba

## استكشاف الأخطاء

### مشكلة: "لم يتم العثور على بيانات المنتج"
- تأكد من أن الرابط صحيح ومن موقع Alibaba
- جرب رابط منتج آخر
- تأكد من أن الصفحة تحتوي على بيانات المنتج

### مشكلة: بطء في التحميل
- هذا طبيعي، حيث يحتاج Puppeteer لتحميل الصفحة كاملة
- تأكد من استقرار اتصال الإنترنت

### مشكلة: عدم ظهور الصور
- بعض الصور قد تكون محمية أو تتطلب تسجيل دخول
- هذا لا يؤثر على باقي البيانات المستخرجة

## الترخيص

هذا المشروع مفتوح المصدر ومخصص للأغراض التعليمية.
