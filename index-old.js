const express = require('express');
const { chromium } = require('playwright');
const path = require('path');
const ProxyManager = require('./proxy-manager');

const app = express();
const PORT = process.env.PORT || 3004;

// Middleware
app.use(express.json());
app.use(express.static('public'));

// Route to serve the main page
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

// Route for progress updates (Server-Sent Events)
app.get('/progress/:sessionId', (req, res) => {
    res.writeHead(200, {
        'Content-Type': 'text/event-stream',
        'Cache-Control': 'no-cache',
        'Connection': 'keep-alive',
        'Access-Control-Allow-Origin': '*'
    });

    const sessionId = req.params.sessionId;

    // Store the response object for this session
    if (!global.progressSessions) {
        global.progressSessions = {};
    }
    global.progressSessions[sessionId] = res;

    // Clean up on client disconnect
    req.on('close', () => {
        delete global.progressSessions[sessionId];
    });
});

// Function to send progress update
function sendProgress(sessionId, progress, message, submessage = '') {
    if (global.progressSessions && global.progressSessions[sessionId]) {
        const data = JSON.stringify({ progress, message, submessage });
        global.progressSessions[sessionId].write(`data: ${data}\n\n`);
    }
}

// Route to extract product data
app.post('/extract-product', async (req, res) => {
    const { url, sessionId } = req.body;

    if (!url) {
        return res.status(400).json({ error: 'URL is required' });
    }

    // Validate if it's an Alibaba URL
    if (!url.includes('alibaba.com')) {
        return res.status(400).json({ error: 'يجب أن يكون الرابط من موقع Alibaba' });
    }

    let browser;
    let page;

    try {
        sendProgress(sessionId, 10, 'جاري تشغيل المتصفح...', 'تحضير البيئة');
        console.log('Starting browser with Playwright...');

        // إعدادات متصفح محسنة للسرعة القصوى
        browser = await chromium.launch({
            headless: true,
            args: [
                '--no-sandbox',
                '--disable-setuid-sandbox',
                '--disable-dev-shm-usage',
                '--disable-gpu',
                '--disable-web-security',
                '--disable-features=VizDisplayCompositor',
                '--disable-background-timer-throttling',
                '--disable-backgrounding-occluded-windows',
                '--disable-renderer-backgrounding',
                '--disable-field-trial-config',
                '--disable-back-forward-cache',
                '--disable-hang-monitor',
                '--disable-ipc-flooding-protection',
                '--disable-prompt-on-repost',
                '--disable-sync',
                '--force-color-profile=srgb',
                '--metrics-recording-only',
                '--no-first-run',
                '--enable-automation',
                '--password-store=basic',
                '--use-mock-keychain',
                '--disable-component-update',
                '--disable-default-apps',
                '--disable-domain-reliability',
                '--disable-extensions',
                '--disable-print-preview',
                '--disable-speech-api',
                '--hide-scrollbars',
                '--mute-audio',
                '--no-default-browser-check',
                '--no-pings',
                '--no-zygote',
                '--single-process'
            ]
        });

        page = await browser.newPage({
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            viewport: { width: 1366, height: 768 },
            ignoreHTTPSErrors: true,
            javaScriptEnabled: true
        });

        sendProgress(sessionId, 25, 'جاري تحميل صفحة المنتج...', 'الاتصال بالخادم');
        console.log('Navigating to URL:', url);

        // تحميل سريع مع Playwright
        await page.goto(url, {
            waitUntil: 'load',
            timeout: 20000
        });

        sendProgress(sessionId, 40, 'جاري انتظار تحميل المحتوى...', 'تحميل العناصر الأساسية');
        // انتظار قصير جداً
        await page.waitForTimeout(1000);
        
        console.log('Extracting product data...');

        // Debug: Check what tables are available
        const availableTables = await page.evaluate(() => {
            const tables = document.querySelectorAll('table');
            return Array.from(tables).map((table, index) => {
                const rows = table.querySelectorAll('tr');
                const classes = table.className;
                const parent = table.parentElement ? table.parentElement.className : '';
                return {
                    index,
                    rowCount: rows.length,
                    classes,
                    parentClasses: parent,
                    hasKeyValue: rows.length > 0 ? Array.from(rows).slice(0, 3).some(row => {
                        const cells = row.querySelectorAll('td');
                        return cells.length >= 2 && cells[0].textContent.trim() && cells[1].textContent.trim();
                    }) : false
                };
            });
        });

        console.log('Available tables:', availableTables);
        
        sendProgress(sessionId, 55, 'جاري تحميل التبويبات...', 'تحميل المحتوى الديناميكي');

        // النقر السريع على التبويبات باستخدام Playwright المحسن
        try {
            // البحث عن جميع التبويبات وتفعيلها بسرعة
            await page.locator('[role="tab"]').first().click({ timeout: 5000 });
            await page.waitForTimeout(500);

            // تفعيل تبويب المواصفات إذا وجد
            const attributeTab = page.locator('[role="tab"]:has-text("attribute"), [role="tab"]:has-text("spec"), [role="tab"]:has-text("detail")').first();
            if (await attributeTab.count() > 0) {
                await attributeTab.click({ timeout: 5000 });
                await page.waitForTimeout(500);
            }

            console.log('Tabs activated quickly');
        } catch (error) {
            console.log('Tab clicking error (continuing):', error.message);
        }

        sendProgress(sessionId, 70, 'جاري تحميل البيانات...', 'استخراج المحتوى');

        // انتظار سريع للعناصر الأساسية فقط
        try {
            await page.waitForSelector('h1, .product-title, table', { timeout: 5000 });
            console.log('Basic elements loaded');
        } catch (e) {
            console.log('Continuing without waiting for specific elements');
        }

        sendProgress(sessionId, 85, 'جاري استخراج البيانات...', 'تحليل العناصر والجداول');
        console.log('Extracting product data with Playwright...');

        // استخراج البيانات باستخدام Playwright المحسن
        const productData = {
            title: '',
            description: '',
            images: [],
            features: []
        };

        // استخراج العنوان بسرعة
        try {
            const titleLocator = page.locator('h1[data-role="titleBox"], .product-title h1, h1').first();
            productData.title = await titleLocator.textContent({ timeout: 3000 }) || '';
            productData.title = productData.title.trim();
        } catch (e) {
            console.log('Title extraction failed, trying alternative method');
        }

        // استخراج الصور بسرعة
        try {
            const imageElements = await page.locator('img[src*="alicdn.com"]').all();
            for (let i = 0; i < Math.min(imageElements.length, 8); i++) {
                try {
                    let src = await imageElements[i].getAttribute('src');
                    if (src && !src.includes('data:image')) {
                        if (src.startsWith('//')) src = 'https:' + src;
                        // تحسين جودة الصورة
                        src = src.replace(/_\d+x\d+\.jpg/, '_400x400.jpg');
                        productData.images.push(src);
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {
            console.log('Image extraction failed');
        }

        // استخراج الوصف والمواصفات بسرعة
        try {
            // استخراج الوصف من الجداول
            const tables = await page.locator('table').all();
            let description = '';

            for (const table of tables.slice(0, 3)) { // فقط أول 3 جداول
                try {
                    const rows = await table.locator('tr').all();
                    if (rows.length > 2) {
                        for (let i = 0; i < Math.min(rows.length, 10); i++) {
                            const cells = await rows[i].locator('td').all();
                            if (cells.length >= 2) {
                                const key = await cells[0].textContent();
                                const value = await cells[1].textContent();
                                if (key && value && key.trim() && value.trim()) {
                                    description += `**${key.trim()}**: ${value.trim()}\n\n`;
                                }
                            }
                        }
                        if (description.length > 100) break; // توقف عند الحصول على وصف كافي
                    }
                } catch (e) {
                    continue;
                }
            }
            productData.description = description.trim();
        } catch (e) {
            console.log('Description extraction failed');
        }

        // استخراج المواصفات بسرعة
        try {
            const attributeItems = await page.locator('.attribute-item, .spec-item, li').all();
            for (let i = 0; i < Math.min(attributeItems.length, 20); i++) {
                try {
                    const text = await attributeItems[i].textContent();
                    if (text && text.trim().length > 3 && text.trim().length < 200) {
                        productData.features.push(text.trim());
                    }
                } catch (e) {
                    continue;
                }
            }
        } catch (e) {
            console.log('Features extraction failed');
        }

        // إذا لم نحصل على عنوان، جرب طريقة أخرى
        if (!productData.title) {
            try {
                productData.title = await page.evaluate(() => {
                    const titleSelectors = ['h1[data-role="titleBox"]', '.product-title h1', 'h1'];
                    for (const selector of titleSelectors) {
                        const element = document.querySelector(selector);
                        if (element && element.textContent.trim()) {
                            return element.textContent.trim();
                        }
                    }
                    return 'منتج من Alibaba';
                });
            } catch (e) {
                productData.title = 'منتج من Alibaba';
            }
        }

            // Try to click on description tab first
            const descriptionTab = document.querySelector('[role="tab"][id*="description"], button[data-testid*="description"]');
            if (descriptionTab) {
                descriptionTab.click();
                // Wait a bit for content to load
                setTimeout(() => {}, 2000);
            }

            // Also try clicking on any tab that might contain description
            const allTabs = document.querySelectorAll('[role="tab"]');
            allTabs.forEach(tab => {
                const tabText = tab.textContent.toLowerCase();
                if (tabText.includes('وصف') || tabText.includes('description') || tabText.includes('detail')) {
                    tab.click();
                    setTimeout(() => {}, 1000);
                }
            });

            // Extract from detailed table - comprehensive search
            let detailTable = null;
            let allCandidateTables = [];

            // Get ALL tables and analyze them
            const allTables = document.querySelectorAll('table');

            allTables.forEach((table) => {
                const rows = table.querySelectorAll('tr');
                if (rows.length >= 2) {
                    let keyValuePairs = 0;
                    let totalContent = 0;

                    // Analyze first few rows to see if it's a description table
                    for (let i = 0; i < Math.min(10, rows.length); i++) {
                        const cells = rows[i].querySelectorAll('td');
                        if (cells.length >= 2) {
                            const key = cells[0].textContent.trim();
                            const value = cells[1].textContent.trim();
                            if (key.length > 0 && value.length > 0) {
                                keyValuePairs++;
                                totalContent += key.length + value.length;
                            }
                        }
                    }

                    // Score the table based on content and structure
                    const score = keyValuePairs * 10 + totalContent;

                    if (keyValuePairs >= 2) {
                        allCandidateTables.push({
                            table,
                            score,
                            keyValuePairs,
                            rowCount: rows.length,
                            classes: table.className,
                            parentClasses: table.parentElement ? table.parentElement.className : ''
                        });
                    }
                }
            });

            // Sort by score and pick the best table
            allCandidateTables.sort((a, b) => b.score - a.score);

            // Prefer tables with specific classes if they exist
            const preferredTable = allCandidateTables.find(t =>
                t.classes.includes('magic-3') ||
                t.classes.includes('hight-light-first-column') ||
                t.parentClasses.includes('ife-detail-decorate-table')
            );

            detailTable = preferredTable ? preferredTable.table :
                         (allCandidateTables.length > 0 ? allCandidateTables[0].table : null);

            if (detailTable) {
                const rows = detailTable.querySelectorAll('tr');
                let tableData = [];

                console.log(`Found description table with ${rows.length} rows`);

                rows.forEach((row, index) => {
                    const cells = row.querySelectorAll('td');
                    if (cells.length >= 2) {
                        const key = cells[0].textContent.trim().replace(/:/g, '');
                        const value = cells[1].textContent.trim();

                        if (key && value && key.length > 0 && value.length > 0) {
                            // Clean up the value text but keep more content
                            const cleanValue = value.replace(/\s+/g, ' ').trim();
                            if (cleanValue.length > 0) {
                                tableData.push(`**${key}**: ${cleanValue}`);
                                console.log(`Row ${index + 1}: ${key} = ${cleanValue.substring(0, 50)}...`);
                            }
                        }
                    }
                });

                if (tableData.length > 0) {
                    description = tableData.join('\n\n');
                    console.log(`Extracted ${tableData.length} description items`);
                } else {
                    console.log('No valid description data found in table');
                }
            } else {
                console.log('No description table found');
            }

            // Fallback to traditional selectors if no table found
            if (!description) {
                const descriptionSelectors = [
                    '[data-state="active"][id*="description"]',
                    '[role="tabpanel"][id*="description"]',
                    '.ma-ref-content .ma-ref-description',
                    '.product-description',
                    '.ma-description',
                    '.description-content',
                    '.product-detail-description',
                    '[data-role="description"]',
                    '.product-overview',
                    '.product-intro',
                    '.detail-desc',
                    '.product-summary'
                ];

                for (const selector of descriptionSelectors) {
                    const descElement = document.querySelector(selector);
                    if (descElement && descElement.textContent.trim()) {
                        let desc = descElement.textContent.trim();
                        // Clean up description
                        desc = desc.replace(/\s+/g, ' ').substring(0, 800);
                        if (desc.length > 50) {
                            description = desc;
                            break;
                        }
                    }
                }
            }

            data.description = description;
            
            // Extract images - Updated for new Alibaba layout
            const imageUrls = new Set();

            // First try to get images from background-image style (new layout)
            const backgroundImageElements = document.querySelectorAll('[style*="background-image"]');
            backgroundImageElements.forEach(element => {
                const style = element.getAttribute('style');
                if (style && style.includes('background-image')) {
                    const match = style.match(/background-image:\s*url\(["']?(.*?)["']?\)/);
                    if (match && match[1]) {
                        let src = match[1];
                        if (src.startsWith('//')) {
                            src = 'https:' + src;
                        }
                        // Clean up image URL for better quality
                        if (src.includes('_80x80.jpg')) {
                            src = src.replace('_80x80.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_50x50.jpg')) {
                            src = src.replace('_50x50.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_100x100.jpg')) {
                            src = src.replace('_100x100.jpg', '_400x400.jpg');
                        }
                        if (src.includes('_220x220.jpg')) {
                            src = src.replace('_220x220.jpg', '_400x400.jpg');
                        }
                        if (src.includes('alicdn.com') && src.includes('.jpg')) {
                            imageUrls.add(src);
                        }
                    }
                }
            });

            // Fallback to traditional img tags if no background images found
            if (imageUrls.size === 0) {
                const imageSelectors = [
                    '.ma-thumb-list img',
                    '.product-images img',
                    '.ma-preview-list img',
                    '.image-gallery img',
                    '.product-gallery img',
                    '.gallery-img img',
                    '.main-image img',
                    'img[src*="alicdn.com"]'
                ];

                for (const selector of imageSelectors) {
                    const images = document.querySelectorAll(selector);
                    images.forEach(img => {
                        let src = img.src || img.getAttribute('data-src') || img.getAttribute('data-lazy-src') || img.getAttribute('data-original');
                        if (src && !src.includes('data:image') && (src.startsWith('http') || src.startsWith('//'))) {
                            // Fix protocol if missing
                            if (src.startsWith('//')) {
                                src = 'https:' + src;
                            }
                            // Clean up image URL for better quality
                            if (src.includes('_80x80.jpg')) {
                                src = src.replace('_80x80.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_50x50.jpg')) {
                                src = src.replace('_50x50.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_100x100.jpg')) {
                                src = src.replace('_100x100.jpg', '_400x400.jpg');
                            }
                            if (src.includes('_220x220.jpg')) {
                                src = src.replace('_220x220.jpg', '_400x400.jpg');
                            }
                            imageUrls.add(src);
                        }
                    });
                    if (imageUrls.size > 0) break;
                }
            }
            
            data.images = Array.from(imageUrls).slice(0, 8); // Limit to 8 images
            
            // Extract features/specifications - Updated for new Alibaba layout
            // Try to click on attributes tab first
            const attributesTab = document.querySelector('[role="tab"][id*="attribute"], button[data-testid*="attribute"]');
            if (attributesTab) {
                attributesTab.click();
                // Wait a bit for content to load
                setTimeout(() => {}, 1000);
            }

            // Extract from attribute-info section (main specifications)
            const attributeInfo = document.querySelector('.attribute-info');
            if (attributeInfo) {
                console.log('Found attribute-info section');
                const attributeSections = attributeInfo.querySelectorAll('h3');
                attributeSections.forEach(section => {
                    const sectionTitle = section.textContent.trim();
                    if (sectionTitle) {
                        data.features.push(`=== ${sectionTitle} ===`);
                        console.log(`Found section: ${sectionTitle}`);

                        // Get the attribute list following this section
                        let nextElement = section.nextElementSibling;
                        if (nextElement && nextElement.classList.contains('attribute-list')) {
                            const items = nextElement.querySelectorAll('.attribute-item');
                            console.log(`Found ${items.length} items in section ${sectionTitle}`);
                            items.forEach(item => {
                                const left = item.querySelector('.left');
                                const right = item.querySelector('.right');
                                if (left && right) {
                                    const key = left.textContent.trim();
                                    const value = right.textContent.trim();
                                    if (key && value) {
                                        data.features.push(`${key}: ${value}`);
                                    }
                                }
                            });
                        }
                    }
                });
            } else {
                console.log('No attribute-info section found');
            }

            // If no attribute-info found, try to extract from other specification sources
            // (Skip description tables as they are now handled in description section)

            // Extract from SKU options (colors, variants) - Additional specifications
            const skuLayout = document.querySelector('[data-testid="sku-layout"]');
            if (skuLayout) {
                data.features.push('=== مواصفات إضافية ===');

                const skuLists = skuLayout.querySelectorAll('[data-testid="sku-list"]');
                skuLists.forEach(list => {
                    const title = list.querySelector('[data-testid="sku-list-title"]');
                    if (title) {
                        const titleText = title.textContent.trim();
                        if (titleText) {
                            data.features.push(titleText);
                        }
                    }
                });

                // Extract shipping info
                const logistic = skuLayout.querySelector('[data-testid="sku-logistic"]');
                if (logistic) {
                    const logisticText = logistic.textContent.trim();
                    if (logisticText && logisticText.length > 10) {
                        data.features.push(`الشحن: ${logisticText.substring(0, 200)}`);
                    }
                }
            }

            // Extract from price and product highlights
            const priceInfo = document.querySelector('[data-testid="product-price"]');
            if (priceInfo) {
                const priceText = priceInfo.textContent.trim();
                if (priceText.includes('US$')) {
                    const priceMatch = priceText.match(/[\d.,]+-[\d.,]+\s*US\$/);
                    if (priceMatch) {
                        data.features.push(`السعر: ${priceMatch[0]}`);
                    }
                }
                if (priceText.includes('الحد الأدنى للطلب')) {
                    const minOrderMatch = priceText.match(/الحد الأدنى للطلب\s+(\d+)\s*قطعة/);
                    if (minOrderMatch) {
                        data.features.push(`الحد الأدنى للطلب: ${minOrderMatch[1]} قطعة`);
                    }
                }
            }

            // Extract from product highlights
            const highlights = document.querySelectorAll('[data-module-name="module_product_highlights"] [data-testid]');
            highlights.forEach(highlight => {
                const text = highlight.textContent.trim();
                if (text && text.length > 3 && text.length < 100 && !text.includes('قابل للتخصيص')) {
                    data.features.push(text);
                }
            });

            // Traditional selectors as fallback
            if (data.features.length === 0) {
                const featureSelectors = [
                    '[data-state="active"][id*="attribute"] li',
                    '[role="tabpanel"][id*="attribute"] li',
                    '.ma-spec-list li',
                    '.product-specs li',
                    '.specifications li',
                    '.product-features li',
                    '.ma-attribute-list li'
                ];

                for (const selector of featureSelectors) {
                    const features = document.querySelectorAll(selector);
                    if (features.length > 0) {
                        features.forEach(feature => {
                            const text = feature.textContent.trim();
                            if (text && text.length > 3 && text.length < 200) {
                                data.features.push(text);
                            }
                        });
                        break;
                    }
                }

                // If still no features found, try to extract from table rows
                if (data.features.length === 0) {
                    const tableRows = document.querySelectorAll('.ma-spec-table tr, .product-table tr, table tr');
                    tableRows.forEach(row => {
                        const cells = row.querySelectorAll('td, th');
                        if (cells.length >= 2) {
                            const key = cells[0].textContent.trim();
                            const value = cells[1].textContent.trim();
                            if (key && value && key.length < 50 && value.length < 100) {
                                data.features.push(`${key}: ${value}`);
                            }
                        }
                    });
                }
            }
            
            return data;
        });
        
        console.log('Product data extracted:', {
            title: productData.title ? 'Found' : 'Not found',
            description: productData.description ? 'Found' : 'Not found',
            images: productData.images.length,
            features: productData.features.length
        });

        // Check if we got enough data, if not retry once
        if (productData.features.length < 10) {
            console.log(`Insufficient data detected (${productData.features.length} features), retrying extraction...`);
            sendProgress(sessionId, 90, 'البيانات غير مكتملة، جاري إعادة المحاولة...', 'انتظار إضافي لتحميل البيانات');

            // النقر على المزيد من التبويبات والانتظار أطول
            const retryTabs = await page.locator('[role="tab"]').all();
            for (const tab of retryTabs) {
                try {
                    const text = await tab.textContent();
                    const textLower = text?.toLowerCase() || '';
                    if (textLower.includes('attribute') || textLower.includes('spec') || textLower.includes('detail') ||
                        textLower.includes('مواصفات') || textLower.includes('السمة') || textLower.includes('كفاءة')) {
                        console.log('Clicking on:', text);
                        await tab.click();
                        await page.waitForTimeout(1000);
                    }
                } catch (error) {
                    console.log('Error clicking retry tab:', error.message);
                }
            }

            // انتظار قصير لتحميل المحتوى
            await page.waitForTimeout(5000);

            // Re-extract data with the same logic
            const retryData = await page.evaluate(() => {
                const data = {
                    title: '',
                    description: '',
                    images: [],
                    features: []
                };

                // Check for attribute-info again
                const attributeInfo = document.querySelector('.attribute-info');
                if (attributeInfo) {
                    console.log('Found attribute-info on retry');
                    const attributeSections = attributeInfo.querySelectorAll('h3');
                    attributeSections.forEach(section => {
                        const sectionTitle = section.textContent.trim();
                        if (sectionTitle) {
                            data.features.push(`=== ${sectionTitle} ===`);

                            let nextElement = section.nextElementSibling;
                            if (nextElement && nextElement.classList.contains('attribute-list')) {
                                const items = nextElement.querySelectorAll('.attribute-item');
                                items.forEach(item => {
                                    const left = item.querySelector('.left');
                                    const right = item.querySelector('.right');
                                    if (left && right) {
                                        const key = left.textContent.trim();
                                        const value = right.textContent.trim();
                                        if (key && value) {
                                            data.features.push(`${key}: ${value}`);
                                        }
                                    }
                                });
                            }
                        }
                    });
                } else {
                    console.log('Still no attribute-info found on retry');
                }

                return data;
            });

            // Use retry data if it's better
            if (retryData.features.length > productData.features.length) {
                console.log(`Retry successful! Got ${retryData.features.length} features instead of ${productData.features.length}`);
                productData.features = [...productData.features, ...retryData.features];
            } else {
                console.log('Retry did not improve results');
            }
        }

        sendProgress(sessionId, 95, 'جاري معالجة النتائج...', 'تنظيف وتنسيق البيانات');

        // Validate extracted data
        if (!productData.title && !productData.description && productData.images.length === 0 && productData.features.length === 0) {
            throw new Error('لم يتم العثور على بيانات المنتج. تأكد من صحة الرابط.');
        }

        // If no title found, try to extract from URL or use a default
        if (!productData.title) {
            const urlParts = url.split('/');
            const lastPart = urlParts[urlParts.length - 1];
            if (lastPart && lastPart.includes('_')) {
                const titleFromUrl = lastPart.split('_')[0].replace(/-/g, ' ');
                productData.title = titleFromUrl || 'منتج من Alibaba';
            } else {
                productData.title = 'منتج من Alibaba';
            }
        }

        sendProgress(sessionId, 100, 'تم الانتهاء!', 'جاري عرض النتائج');

        // Clean up the session
        setTimeout(() => {
            if (global.progressSessions && global.progressSessions[sessionId]) {
                global.progressSessions[sessionId].end();
                delete global.progressSessions[sessionId];
            }
        }, 2000);

        res.json(productData);
        
    } catch (error) {
        console.error('Error extracting product data:', error);
        res.status(500).json({ 
            error: 'حدث خطأ أثناء استخراج البيانات: ' + error.message 
        });
    } finally {
        if (context) {
            await context.close();
        }
        if (browser) {
            await browser.close();
        }
    }
});

// Start server
app.listen(PORT, '0.0.0.0', () => {
    console.log(`🚀 Server is running on http://0.0.0.0:${PORT}`);
    console.log(`📝 Access the application at: http://localhost:${PORT}`);
    console.log(`🌐 External access: http://**************:${PORT}`);
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});

process.on('SIGTERM', () => {
    console.log('\n👋 Shutting down server...');
    process.exit(0);
});
